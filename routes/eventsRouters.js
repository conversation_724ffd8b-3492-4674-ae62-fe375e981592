const express = require("express");
const router = express.Router();
const controller = require("../controllers/eventTaskController");

// List tasks
router.get("/", controller.listTasks);

// Create task
router.post("/", controller.createTask);

// Get single task
router.get("/:id", controller.getTask);

// Complete task
router.put("/:id/complete", controller.completeTask);

// Decline task
router.put("/:id/decline", controller.declineTaskRoute);

// Modify task (request changes)
router.put("/:id/modify", controller.modifyTaskRoute);

// Get records by type
router.get("/records/:type", controller.recordsByType);

module.exports = router;