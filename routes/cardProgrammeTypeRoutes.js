const express = require("express");
const router = express.Router();
const controller = require("../controllers/cardProgrammeTypeController");

// Route to create a new card programme type
router.post("/programme-types", controller.createCardProgrammeType);

// Route to fetch all card programme types
router.get("/programme-types", controller.getCardProgrammeTypes);

// Soft delete
router.delete("/programme-types/:id", controller.deleteCardProgrammeType);

// Approval endpoints
router.post("/approve", controller.approve);
router.post("/decline", controller.decline);
router.post("/modify", controller.modify);

module.exports = router;
