const express = require("express");
const router = express.Router();
const controller = require("../controllers/cardTypeController");

// Create
router.post("/", controller.createCardType);

// List
router.get("/", controller.getCardTypes);

// Soft delete
router.delete("/:id", controller.deleteCardType);

// Approval endpoints
router.post("/approve", controller.approve);
router.post("/decline", controller.decline);
router.post("/modify", controller.modify);

module.exports = router;
