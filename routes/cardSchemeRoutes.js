const express = require("express");
const router = express.Router();
const multer = require("multer");
const path = require("path");
const controller = require("../controllers/cardSchemeController");

// multer storage (kept in route so controller stays pure)
const storage = multer.diskStorage({
  destination: (req, file, cb) => cb(null, path.join('uploads')),
  filename: (req, file, cb) => cb(null, Date.now() + path.extname(file.originalname)),
});
const upload = multer({ storage });

// Create with optional logo upload
router.post("/schemes", upload.single("scheme_logo"), controller.createCardScheme);

// List
router.get("/schemes", controller.getCardSchemes);

// Soft delete
router.delete("/schemes/:id", controller.deleteCardScheme);

// Approval endpoints
router.post("/approve-scheme", controller.approveScheme);
router.post("/decline-scheme", controller.declineScheme);
router.post("/modify-scheme", controller.modifyScheme);

// Update (with optional logo)
router.put("/schemes/:id", upload.single("scheme_logo"), controller.updateCardScheme);

module.exports = router;
