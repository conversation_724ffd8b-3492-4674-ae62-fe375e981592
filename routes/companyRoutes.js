const express = require('express');
const router = express.Router();
const controller = require('../controllers/companyController');
const { body, validationResult } = require('express-validator');

// Create company
router.post('/save', controller.createCompany);

// List companies
router.get('/', controller.listCompanies);

// Get company by id
router.get('/:id', controller.getCompanyById);

// Get company by ryvyl id
router.get('/ryvyl/:ryvylID', controller.getCompanyByRyvylId);

// Banking clients
router.get('/:id/bankingClients', controller.getBankingClients);

// Activate
router.get('/:recordId/activate', controller.activateCompany);

// Disable / Enable
router.post('/disable', controller.disableCompany);
router.post('/enable', controller.enableCompany);

// Update company
router.put('/:id', controller.updateCompany);

// Update alert settings
router.post('/:id/alert-settings', controller.updateAlertSettings);

// Create user for company (validation in route)
router.post(
  '/:id/createUser',
  [
    body('name').trim().notEmpty().withMessage('Name is required'),
    body('email').trim().isEmail().withMessage('Email is invalid'),
    body('status').notEmpty().withMessage('Status is required'),
  ],
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) return res.status(400).json({ errors: errors.array() });
    return controller.createUserForCompany(req, res, next);
  }
);

module.exports = router;
