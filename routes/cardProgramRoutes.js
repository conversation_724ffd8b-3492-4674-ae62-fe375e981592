const express = require('express');
const router = express.Router();
const { saveCardProgram, getAllCardPrograms, getCardProgramDetails, updateCardProgram, deleteCardProgramme} = require('../controllers/cardProgramController');

// POST route to save card program data
router.post('/save-data', saveCardProgram);
router.get("/", getAllCardPrograms)
router.get("/:id", getCardProgramDetails)
router.patch("/:id", updateCardProgram)
router.delete("/:id", deleteCardProgramme)

module.exports = router;
