const express = require('express');
const router = express.Router();
const PolishPost = require('../models/PolishPost');

// Middleware to handle async errors
const asyncHandler = fn => (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
};

/**
 * @route   GET /api/polish-post
 * @desc    Get all Polish Post configurations
 * @access  Public
 */
router.get('/', asyncHandler(async (req, res) => {
    const { active } = req.query;

    let query = {};
    if (active !== undefined) {
        query.isActive = active === 'true';
    }

    const polishPosts = await PolishPost.find(query).sort({ createdAt: -1 });
    res.json(polishPosts);
}));

/**
 * @route   GET /api/polish-post/:id
 * @desc    Get Polish Post configuration by ID
 * @access  Public
 */
router.get('/:id', asyncHandler(async (req, res) => {
    const polishPost = await PolishPost.findById(req.params.id);

    if (!polishPost) {
        return res.status(404).json({ message: 'Polish Post configuration not found' });
    }

    res.json(polishPost);
}));

/**
 * @route   POST /api/polish-post
 * @desc    Create a new Polish Post configuration
 * @access  Private
 */
router.post('/', asyncHandler(async (req, res) => {
    const { zoneId, deliveryTime, methodId, trackingDays, weightRates, isActive } = req.body;

    // Validate required fields
    if (!zoneId || !deliveryTime || !methodId || !weightRates || !Array.isArray(weightRates) || weightRates.length === 0) {
        return res.status(400).json({ message: 'Missing required fields' });
    }

    // Validate weight rates
    for (const wr of weightRates) {
        if (typeof wr.weight !== 'number' || wr.weight <= 0 || typeof wr.rate !== 'number' || wr.rate <= 0) {
            return res.status(400).json({ message: 'Invalid weight or rate values' });
        }
    }

    const newPolishPost = new PolishPost({
        zoneId,
        deliveryTime,
        methodId,
        trackingDays: trackingDays || 14,
        weightRates,
        isActive: isActive !== undefined ? isActive : true
    });

    const savedPolishPost = await newPolishPost.save();
    res.status(201).json(savedPolishPost);
}));

/**
 * @route   PUT /api/polish-post/:id
 * @desc    Update a Polish Post configuration
 * @access  Private
 */
router.put('/:id', asyncHandler(async (req, res) => {
    const { zoneId, deliveryTime, methodId, trackingDays, weightRates, isActive } = req.body;

    // Find the Polish Post configuration
    let polishPost = await PolishPost.findById(req.params.id);

    if (!polishPost) {
        return res.status(404).json({ message: 'Polish Post configuration not found' });
    }

    // Validate required fields
    if (!zoneId || !deliveryTime || !methodId || !weightRates || !Array.isArray(weightRates) || weightRates.length === 0) {
        return res.status(400).json({ message: 'Missing required fields' });
    }

    // Validate weight rates
    for (const wr of weightRates) {
        if (typeof wr.weight !== 'number' || wr.weight <= 0 || typeof wr.rate !== 'number' || wr.rate <= 0) {
            return res.status(400).json({ message: 'Invalid weight or rate values' });
        }
    }

    // Update the Polish Post configuration
    polishPost.zoneId = zoneId;
    polishPost.deliveryTime = deliveryTime;
    polishPost.methodId = methodId;
    polishPost.trackingDays = trackingDays || polishPost.trackingDays;
    polishPost.weightRates = weightRates;
    polishPost.isActive = isActive !== undefined ? isActive : polishPost.isActive;

    const updatedPolishPost = await polishPost.save();
    res.json(updatedPolishPost);
}));

/**
 * @route   DELETE /api/polish-post/:id
 * @desc    Delete a Polish Post configuration
 * @access  Private
 */
router.delete('/:id', asyncHandler(async (req, res) => {
    const polishPost = await PolishPost.findById(req.params.id);

    if (!polishPost) {
        return res.status(404).json({ message: 'Polish Post configuration not found' });
    }

    await polishPost.remove();
    res.json({ message: 'Polish Post configuration removed' });
}));

/**
 * @route   GET /api/polish-post/zone/:zoneId
 * @desc    Get Polish Post configurations by zone
 * @access  Public
 */
router.get('/zone/:zoneId', asyncHandler(async (req, res) => {
    const { zoneId } = req.params;
    const { active } = req.query;

    let query = { zoneId };
    if (active !== undefined) {
        query.isActive = active === 'true';
    }

    const polishPosts = await PolishPost.find(query).sort({ createdAt: -1 });
    res.json(polishPosts);
}));

/**
 * @route   GET /api/polish-post/rates/:zoneId/:weight
 * @desc    Get appropriate rate for a zone and weight
 * @access  Public
 */
router.get('/rates/:zoneId/:weight', asyncHandler(async (req, res) => {
    const { zoneId, weight } = req.params;
    const weightValue = parseFloat(weight);

    if (isNaN(weightValue) || weightValue <= 0) {
        return res.status(400).json({ message: 'Invalid weight value' });
    }

    // Find active configuration for the zone
    const polishPost = await PolishPost.findOne({
        zoneId,
        isActive: true
    });

    if (!polishPost) {
        return res.status(404).json({ message: 'No active configuration found for this zone' });
    }

    // Sort weight rates by weight in ascending order
    const sortedWeightRates = [...polishPost.weightRates].sort((a, b) => a.weight - b.weight);

    // Find the appropriate rate for the given weight
    let applicableRate = null;

    for (const wr of sortedWeightRates) {
        if (weightValue <= wr.weight) {
            applicableRate = wr;
            break;
        }
    }

    // If no applicable rate found, use the highest weight rate
    if (!applicableRate && sortedWeightRates.length > 0) {
        applicableRate = sortedWeightRates[sortedWeightRates.length - 1];
    }

    if (!applicableRate) {
        return res.status(404).json({ message: 'No applicable rate found for this weight' });
    }

    res.json({
        zone: polishPost.zoneId,
        zoneDescription: polishPost.zoneDescription,
        weight: weightValue,
        applicableWeight: applicableRate.weight,
        rate: applicableRate.rate,
        deliveryTime: polishPost.deliveryTime,
        methodId: polishPost.methodId
    });
}));

module.exports = router;