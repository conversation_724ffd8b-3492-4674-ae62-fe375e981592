const express = require("express");
const crypto = require("crypto");

const router = express.Router();

// Function to send a POST request
const {   sendPostRequest } = require("../config/ApiInstense");

// GET Route to retrieve and decrypt card number
router.post("/:id/card-number", async function (req, res) {
    const { id } = req.params;
    const { expDate } = req.body; // The expiration date from the request body

    try {
        // Generate EC key pair using SECP256R1 (prime256v1)
        const { privateKey, publicKey } = crypto.generateKeyPairSync("ec", {
            namedCurve: "prime256v1",  // Correct curve name
            publicKeyEncoding: { type: "spki", format: "der" },  // DER format for public key
            privateKeyEncoding: { type: "pkcs8", format: "der" }  // Use 'der' for private key
        });

        // Convert public key to Base64
        const publicKeyBase64 = Buffer.from(publicKey).toString("base64");

        // Step 2: Authenticate & Get JWT Token
        const authUrl = `https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/${id}/auth`;
        const authResponse = await sendPostRequest(authUrl, { resource: "CARD_NUMBER", securityKey: publicKeyBase64 });

        if (!authResponse.accessToken) {
            throw new Error("Authentication failed. No access token received.");
        }

        // Decode JWT to get Card Info URL
        const jwtParts = authResponse.accessToken.split(".");
        const payload = JSON.parse(Buffer.from(jwtParts[1], "base64").toString()); // Decode payload
        const cardInfoUrl = payload.vsu;

        // Step 3: Fetch Encrypted Card Data
        const headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "Accept": "application/json;charset=UTF-8",
            "Authorization": `Bearer ${authResponse.accessToken}`,
        };
        const request = `{"expDate": "${expDate}"}`;
        const cardDataResponse = await sendPostRequest(cardInfoUrl, request, headers);
        const encryptedData = Buffer.from(cardDataResponse.cardNumber, "base64");
        const securityKey = Buffer.from(cardDataResponse.securityKey, "base64");
        const iv = Buffer.from(cardDataResponse.iv, "base64");

        // Step 4: Decrypt the Card Number
        const serverPublicKey = crypto.createPublicKey({
            key: securityKey,
            format: "der",
            type: "spki"
        });

        // Convert private key to a compatible format
        const privateKeyObject = crypto.createPrivateKey({
            key: privateKey,
            format: 'der',
            type: 'pkcs8'
        });

        // Compute Shared Secret using ECDH
        const sharedSecret = crypto.diffieHellman({
            privateKey: privateKeyObject,
            publicKey: serverPublicKey,
        });

        // **(1)** Try Directly Using Shared Secret Without HKDF for Debugging:
        const cipherKey = sharedSecret.slice(0, 32);  // Use the first 32 bytes of the shared secret for AES key

        // AES-GCM Decryption
        const decipher = crypto.createDecipheriv("aes-256-gcm", cipherKey, iv);
        const tag = encryptedData.slice(-16); // Extract the authentication tag from the last 16 bytes

        decipher.setAuthTag(tag); // Set the authentication tag

        // Decrypt the card number (without the tag)
        const decryptedCardNumber = Buffer.concat([
            decipher.update(encryptedData.slice(0, -16)),  // Remove the tag from the encrypted data
            decipher.final(),
        ]).toString("utf8");

        // Send back the decrypted card number
        res.status(200).json({
            success: true,
            decryptedCardNumber,
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Error retrieving and decrypting card details",
            error: error.message,
        });
    }
});



router.post("/:id/cvv", async function (req, res) {
    const { id } = req.params;
    const { expDate } = req.body; // The expiration date from the request body

    try {
        // Generate EC key pair using SECP256R1 (prime256v1)
        const { privateKey, publicKey } = crypto.generateKeyPairSync("ec", {
            namedCurve: "prime256v1",  // Correct curve name
            publicKeyEncoding: { type: "spki", format: "der" },  // DER format for public key
            privateKeyEncoding: { type: "pkcs8", format: "der" }  // Use 'der' for private key
        });

        // Convert public key to Base64
        const publicKeyBase64 = Buffer.from(publicKey).toString("base64");

        // Step 2: Authenticate & Get JWT Token
        const authUrl = `https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/${id}/auth`;
        const authResponse = await sendPostRequest(authUrl, { resource: "CVV2", securityKey: publicKeyBase64 });

        if (!authResponse.accessToken) {
            throw new Error("Authentication failed. No access token received.");
        }

        // Decode JWT to get Card Info URL
        const jwtParts = authResponse.accessToken.split(".");
        const payload = JSON.parse(Buffer.from(jwtParts[1], "base64").toString()); // Decode payload
        const cardInfoUrl = payload.vsu;

        // Step 3: Fetch Encrypted Card Data
        const headers = {
            "Content-Type": "application/json;charset=UTF-8",
            "Accept": "application/json;charset=UTF-8",
            "Authorization": `Bearer ${authResponse.accessToken}`,
        };

        const request = `{"expDate": "${expDate}"}`;
        const cardDataResponse = await sendPostRequest(cardInfoUrl, request, headers);
        const encryptedData = Buffer.from(cardDataResponse.cvv2, "base64");
        const securityKey = Buffer.from(cardDataResponse.securityKey, "base64");
        const iv = Buffer.from(cardDataResponse.iv, "base64");

        // Step 4: Decrypt the Card Number
        const serverPublicKey = crypto.createPublicKey({
            key: securityKey,
            format: "der",
            type: "spki"
        });

        // Convert private key to a compatible format
        const privateKeyObject = crypto.createPrivateKey({
            key: privateKey,
            format: 'der',
            type: 'pkcs8'
        });

        // Compute Shared Secret using ECDH
        const sharedSecret = crypto.diffieHellman({
            privateKey: privateKeyObject,
            publicKey: serverPublicKey,
        });

        // **(1)** Try Directly Using Shared Secret Without HKDF for Debugging:
        const cipherKey = sharedSecret.slice(0, 32);  // Use the first 32 bytes of the shared secret for AES key

        // AES-GCM Decryption
        const decipher = crypto.createDecipheriv("aes-256-gcm", cipherKey, iv);
        const tag = encryptedData.slice(-16); // Extract the authentication tag from the last 16 bytes

        decipher.setAuthTag(tag); // Set the authentication tag

        // Decrypt the card number (without the tag)
        const decryptedCardCvv = Buffer.concat([
            decipher.update(encryptedData.slice(0, -16)),  // Remove the tag from the encrypted data
            decipher.final(),
        ]).toString("utf8");

        // Send back the decrypted card number
        res.status(200).json({
            success: true,
            decryptedCardCvv,
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Error retrieving and decrypting card details",
            error: error.message,
        });
    }
});








module.exports = router;
