const express = require("express");
const router = express.Router();
const controller = require("../controllers/activityController");

// Single activity
router.post("/", controller.validate<PERSON><PERSON><PERSON><PERSON>, controller.validateActivityData, controller.createActivity);

// Batch activities
router.post("/batch", controller.validate<PERSON><PERSON><PERSON><PERSON>, controller.createBatchActivities);

// List / filter activities
router.get("/", controller.validate<PERSON><PERSON><PERSON><PERSON>, controller.listActivities);

// Stats
router.get("/stats", controller.validateApi<PERSON>ey, controller.stats);

// Popular pages
router.get("/popular-pages", controller.validateApi<PERSON>ey, controller.popularPages);

// User activities
router.get("/user/:userId", controller.validateApi<PERSON>ey, controller.userActivities);

 

// Health check
router.get("/health", controller.healthCheck);

module.exports = router;