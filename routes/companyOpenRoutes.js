const express = require('express');
const router = express.Router();
const Company = require('../models/company');
const contact = require('../models/Contact');
const CardProgram = require('../models/CardProgram');
const Country = require("../models/Countries");
const User = require('../models/user');
const {registerUser} = require("../controllers/userController");
const {body, validationResult} = require("express-validator");
const {sendPostRequest} = require("../config/ApiInstense");
const Account = require("../models/Account");
const {countries} = require("../config/currencies");

router.post(
    '/register',
    [
        body('name').notEmpty().withMessage('Name is required'),
        body('email').isEmail().withMessage('Email is invalid'),
        body('status').notEmpty().withMessage('Status is required'),
    ],
    async (req, res) => {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({errors: errors.array()});
        }

        const {name, email, roles, status, dashboard, recordId} = req.body;


        try {
            const newUser = await registerUser(name, email, roles, status, dashboard, recordId);
            res.status(201).json({message: 'User registered successfully', user: newUser});
        } catch (error) {
            res.status(400).json({message: error.message});
        }
    }
);

// GET route to fetch all company data
router.get('/', async (req, res) => {
    try {
        // Fetch all companies from MongoDB
        const companies = await Company.find().sort({created_at: -1});
        // Return the list of companies
        res.status(200).json({
            success: true,
            data: companies
        });
    } catch (err) {
        // Handle errors
        res.status(500).json({
            success: false,
            message: 'Failed to fetch company data',
            error: err.message
        });
    }
});

router.get('/company/:id', async (req, res) => {
    const {id} = req.params;

    try {
        let company = await Company.findById(id);

        if (!company) {
            return res.status(404).json({message: 'Company not found'});
        }

        // If ryvyl_id doesn't exist, generate and update it
        if (!company.ryvyl_id) {
            let newRyvylId;
            let exists = true;

            // Keep generating until a unique ryvyl_id is found
            while (exists) {
                newRyvylId = generateRyvylId();
                const existing = await Company.findOne({ryvyl_id: newRyvylId});
                if (!existing) {
                    exists = false;
                }
            }

            company.ryvyl_id = newRyvylId;
            await company.save();
        }

        const cardProgram = await CardProgram.find({company: id}).sort({created_at: -1})
            .populate('company')
            .populate('cardScheme')
            .populate('programmeType')
            .populate('programManagerType')
            .populate('binType')
            .populate('binRangeId')
            .populate('created_by')
            .populate('productVersionName');

        res.json({company, cip: cardProgram});
    } catch (error) {
        res.status(500).json({message: 'Error retrieving Company', error});
    }
});


router.get("/country", async (req, res) => {
    try {
        // Fetch only countries where deleted_at is null
        const activeCountries = await Country.find({deleted_at: null}).sort({created_at: -1});
        res.json(activeCountries)
    } catch (err) {
        res.status(500).json({message: "Failed to fetch countries"+err?.message})
    }
})


router.post('/save', async (req, res) => {
    try {
        const companyData = {
            ...req.body,
            ryvyl_id: generateRyvylId(),
        };

        const company = new Company(companyData);

        const clientData = {
            clientCode: company.ryvyl_id,
            firstName: company.admin_first_name,
            lastName: company.admin_last_name,
            phoneNumber: company.admin_phone,
            address: {
                street: company.operational_address.street,
                buildingNumber: company.operational_address.building_number,
                apartmentNumber: company.operational_address.apartment_number,
                city: company.operational_address.city,
                country: getCountryNumber(company.operational_address.country),
                zipCode: company.operational_address.postal_code,
            },
        };

        const clientUrl = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/clients";

        const result = await sendPostRequest(clientUrl, clientData);

        if (result.status !== "APPROVED") {
            return res.status(400).json({
                success: false,
                message: "Onboarding not approved by external API",
                apiStatus: result.status,
            });
        }

        // // External client created — register user, save company, create account.
        // await registerUser(
        //     company.company_name,
        //     company.company_email,

        //     "active",
        //     "programmeManager",
        //     company._id


        await company.save();


        await createAccount(company.ryvyl_id, company._id);

        const newContact = new contact({
            name: `${company.admin_first_name} ${company.admin_last_name}`,
            email: company.admin_email,
            phone: company.admin_phone,
            role: "Administrator",
            company: company._id,
        });

        await newContact.save();

        res.status(201).json({ success: true, data: company });

    } catch (err) {
        res.status(500).json({ success: false, message: err.message });
    }
});

async function createAccount(clientId, companyId) {
    const clientData = JSON.stringify({
        currencyCode: "EUR",
        accNo: "**********************",
        owners: [
            {
                clientCode: clientId,
                relationship: "OWN",
            },
        ],
    });

    const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/accounts/debitAccount";

    try {
        const result = await sendPostRequest(url, clientData);

        if (!result.accNo) {
            return;
        }

        const accountData = {
            accountNumber: result.accNo,
            status: result.status,
            isCompany: true,
            company: companyId,
            currencyCode: result.currencyCode,
            currencyName: result.currencyName,
            owners: result.owners.map((owner) => ({
                clientCode: clientId,
                relationship: owner.relationship,
                mainOwner: owner.mainOwner,
            })),
        };

        const newAccount = new Account(accountData);
        await newAccount.save();
    } catch (e) {
        console.error("Error creating account:", e.message);
    }
}


router.get('/checkEmail/:email/', async (req, res) => {
    try {
        const {email} = req.params;

        // Check if email exists
        const existingUser = await User.findOne({email});
        if (existingUser) {
            return res.status(400).json({message: "Email already exists"});
        }

        res.json({message: "Email available"});
    } catch (error) {
        return res.status(500).json({message: "Server error", error: error.message});
    }
});


const generateRyvylId = () => {
    const randomNumber = Math.floor(******** + Math.random() * ********); // ensures 8 digits
    return `RYVL-${randomNumber}`;
};



function getCountryNumber(code) {
    if (!code) return null
    return countries.find((c) => c.code.toUpperCase() === code.toUpperCase() || c.name.toUpperCase() === code.toUpperCase())?.isoNumeric
}
module.exports = router;

