const express = require('express');
const router = express.Router();
const controller = require('../controllers/binVariantController');

// Create
router.post('/', controller.createBinVariant);

// List
router.get('/', controller.listBinVariants);

// Soft delete
router.delete('/:id', controller.deleteBinVariant);

// Approve / Decline / Modify
router.post('/approve', controller.approveBinVariant);
router.post('/decline', controller.declineBinVariant);
router.post('/modify', controller.modifyBinVariant);

// Update
router.put('/:id', controller.updateBinVariant);

module.exports = router;