const express = require("express")
const router = express.Router()
const WebhookLog = require("../models/WebhookLog")
const webhookLogController = require("../controllers/webhookLogController")

// GET all webhook logs with pagination and filtering
router.get("/", webhookLogController.getWebhookLogs)

// GET single webhook log by ID
router.get("/:id", async (req, res) => {
    try {
        const log = await WebhookLog.findById(req.params.id)
        if (!log) {
            return res.status(404).json({ error: "Webhook log not found" })
        }
        res.json(log)
    } catch (error) {
        res.status(500).json({ error: error.message })
    }
})

// POST create new webhook log
router.post("/", async (req, res) => {
    try {
        const log = new WebhookLog(req.body)
        await log.save()
        res.status(201).json(log)
    } catch (error) {
        res.status(400).json({ error: error.message })
    }
})

// PUT update webhook log
router.put("/:id", async (req, res) => {
    try {
        const log = await WebhookLog.findByIdAndUpdate(req.params.id, req.body, { new: true, runValidators: true })
        if (!log) {
            return res.status(404).json({ error: "Webhook log not found" })
        }
        res.json(log)
    } catch (error) {
        res.status(400).json({ error: error.message })
    }
})

// DELETE webhook log
router.delete("/:id", async (req, res) => {
    try {
        const log = await WebhookLog.findByIdAndDelete(req.params.id)
        if (!log) {
            return res.status(404).json({ error: "Webhook log not found" })
        }
        res.json({ message: "Webhook log deleted successfully" })
    } catch (error) {
        res.status(500).json({ error: error.message })
    }
})

// GET webhook statistics
router.get("/stats/summary", async (req, res) => {
    try {
        const stats = await WebhookLog.aggregate([
            {
                $group: {
                    _id: null,
                    total: { $sum: 1 },
                    successful: {
                        $sum: {
                            $cond: [{ $and: [{ $gte: ["$responseStatus", 200] }, { $lt: ["$responseStatus", 300] }] }, 1, 0],
                        },
                    },
                    failed: {
                        $sum: {
                            $cond: [{ $or: [{ $lt: ["$responseStatus", 200] }, { $gte: ["$responseStatus", 400] }] }, 1, 0],
                        },
                    },
                },
            },
        ])

        const eventStats = await WebhookLog.aggregate([
            {
                $group: {
                    _id: "$event",
                    count: { $sum: 1 },
                },
            },
            { $sort: { count: -1 } },
        ])

        res.json({
            summary: stats[0] || { total: 0, successful: 0, failed: 0 },
            eventBreakdown: eventStats,
        })
    } catch (error) {
        res.status(500).json({ error: error.message })
    }
})

module.exports = router
