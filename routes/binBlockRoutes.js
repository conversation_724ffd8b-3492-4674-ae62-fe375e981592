const express = require("express");
const router = express.Router();
const controller = require("../controllers/binBlockController");

// create
router.post("/", controller.createBinBlock);

// list
router.get("/", controller.listBinBlocks);

// global usage (place before :id)
router.get("/global-usage", controller.globalUsage);

// bins per company
router.get("/bins-per-company", controller.binsPerCompany);

// cip by programme id
router.get("/cip/:id", controller.cipByProgrammeId);

// single
router.get("/:id", controller.getBinBlockById);

// update
router.put("/:id", controller.updateBinBlock);

// delete
router.delete("/:id", controller.deleteBinBlock);

module.exports = router;
