const express = require('express');
const router = express.Router();
const { secureUpload } = require('../middleware/file-upload');
const authenticateToken= require('../middleware/authMiddleware');

// POST /api/upload
router.post('/upload', authenticateToken, secureUpload, (req, res) => {
    res.json({
        message: 'File uploaded and scanned successfully',
        filePath: req.savedFilePath.replace(/\\/g, '/')
    });
});

module.exports = router;
