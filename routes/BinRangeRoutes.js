const express = require("express");
const controller = require("../controllers/binRangeController");
const router = express.Router();

// Routes
router.route("/").post(controller.createBinRange).get(controller.getAllBinRanges);

router
    .route("/:id")
    .get(controller.getBinRangeById)
    .put(controller.updateBinRange)
    .delete(controller.deleteBinRange);



    // Approval route
router.post("/approve", controller.approveBinRange);
module.exports = router;

