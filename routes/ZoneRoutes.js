const express = require("express")
const validateZone = require("../middleware/validateZone")
const zoneController = require("../controllers/zoneController")
const router = express.Router()

// GET all zones
router.get("/", zoneController.getAllZones)

// GET a specific zone by ID
router.get("/:id", zoneController.getZoneById)

// POST a new zone
router.post("/", validateZone, zoneController.createZone)

// PUT (update) a zone
router.put("/:id", validateZone, zoneController.updateZone)

// DELETE a zone
router.delete("/:id", zoneController.deleteZone)

module.exports = router
