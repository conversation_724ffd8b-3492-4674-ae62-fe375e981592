const express = require("express");
const router = express.Router();
const controller = require("../controllers/b2bController");
const { body, validationResult } = require("express-validator");

// List / get
router.get("/", controller.listAccounts);
router.get("/:id", controller.getAccount);

// Create / update / delete
router.post("/", controller.createAccount);
router.put("/:id", controller.updateAccount);
router.delete("/:id", controller.deleteAccount);

// Permissions (validation kept in route)
router.post(
    "/:id/updatePermissions",
    [
        body('permissions').isArray().withMessage('Permissions must be an array'),
        body('permissionAudit').isArray().withMessage('Permission audit must be an array'),
    ],
    async (req, res, next) => {
        const errors = validationResult(req);
        if (!errors.isEmpty()) return res.status(400).json({ errors: errors.array() });
        return controller.updatePermissions(req, res, next);
    }
);

// Cards
router.post("/createCard/physical", controller.createPhysicalCard);
router.post("/createCard/virtual", controller.createVirtualCard);

router.get("/cards/parent/:parentId", controller.getCardsByParent);
router.get("/cards/:id", controller.getCardById);

// Cardholders and cardholder cards
router.get("/:id/cardholders", controller.getCardholders);
router.get("/cardholder/:id/cards", controller.getCardholderCards);

// Products
router.post("/:id/products/assign", controller.assignProducts);
router.get("/:id/products", controller.getProducts);
router.delete("/:id/products/:productId", controller.unassignProduct);

// Update address
router.post("/updateAddress", controller.updateAddress);

// simple endpoint kept (originally returned empty)
router.get("/all", (req, res) => {
    return res.status(200).json();
});

module.exports = router;
