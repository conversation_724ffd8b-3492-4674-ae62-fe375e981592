const express = require("express")
const validateDeliveryMethod = require("../middleware/validateDeliveryMethod")
const deliveryMethodController = require("../controllers/deliveryMethodController")
const router = express.Router()

// GET all delivery methods
router.get("/", deliveryMethodController.getAllDeliveryMethods)

// GET a specific delivery method by ID
router.get("/:id", deliveryMethodController.getDeliveryMethodById)

// POST a new delivery method
router.post("/", validateDeliveryMethod, deliveryMethodController.createDeliveryMethod)

// PUT (update) a delivery method
router.put("/:id", validateDeliveryMethod, deliveryMethodController.updateDeliveryMethod)

// DELETE a delivery method
router.delete("/:id", deliveryMethodController.deleteDeliveryMethod)

module.exports = router
