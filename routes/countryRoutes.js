// routes/countries.js

const express = require("express");
const router = express.Router();
const controller = require("../controllers/countryController");

// List active countries
router.get("/", controller.listCountries);

// Create country
router.post("/", controller.createCountry);

// Soft delete
router.delete("/:id", controller.deleteCountry);

// Update active status
router.patch("/:id/status", controller.patchStatus);

// Approval / workflow
router.post("/approve", controller.approve);
router.post("/decline", controller.decline);
router.post("/modify", controller.modify);

module.exports = router;
