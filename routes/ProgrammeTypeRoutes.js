const express = require('express');
const router = express.Router();
const ProgrammeType = require('../models/ProgrammeType')
const {createTask, updateTaskStatus} = require("../config/EventHandler");
const User = require("../models/user");

router.post('/', async (req, res) => {
    try {
        const progType = new ProgrammeType(req.body);

        var p = await progType.save();
        const user = await User.findById(req.body.created_by);

        const taskData = {
            refId: p._id,
            type: 'Programme Type',
            title: user.name + ' requested a new Programme Type "' + req.body.type + '"',
            date: new Date(),
            user: user._id,
            ipAddress: '************',
        };
        await createTask(taskData)


        res.status(201).json({success: true, data: progType});
    } catch (err) {
        res.status(400).json({success: false, message: err.message});
    }
});

router.get('/', async (req, res) => {
    try {
        const progType = await ProgrammeType.find({deleted_at: null}).populate("created_by").sort({created_at: -1});
        res.json(progType)
    } catch (err) {
        res.status(400).json({success: false, message: err.message});
    }
});


router.delete("/:id", async (req, res) => {
    const {id} = req.params;

    try {
        const deletedUser = await ProgrammeType.findByIdAndUpdate(id, {deleted_at: new Date()}, // Set deleted_at field
            {new: true})

        if (!deletedUser) {
            return res.status(404).json({message: 'Record not found'});
        }

        res.json({message: 'Record deleted successfully'});
    } catch (error) {
        res.status(500).json({message: 'Error deleting Record', error});
    }
});


router.post("/approve", async (req, res) => {
    try {
        const {entityId} = req.body;

        // Perform approval logic, e.g., update the database
        if (!entityId) {
            return res.status(400).json({message: "record ID is required."});
        }
        const record = await ProgrammeType.findById(entityId);
        if (!record) {
            return res.status(404).json({message: "record not found."});
        }
        // Parse and increment the version number
        let currentVersion = parseFloat(record.version) || 0.0; // Default to 0.0 if version is not set
        let updatedVersion = (currentVersion + 0.1).toFixed(1); // Increment and format to one decimal place

        // Update the record status and version
        const updatedrecord = await ProgrammeType.findByIdAndUpdate(entityId, {
                status: "active",
                version: updatedVersion
            }, {new: true} // Return the updated document
        );

        res.status(200).json({message: "record approved successfully."});
    } catch (error) {
        res.status(500).json({message: "Internal server error."});
    }
});


router.post("/decline", async (req, res) => {
    try {
        const {entityId, reason} = req.body;

        if (!entityId || !reason) {
            return res.status(400).json({message: "record ID and reason are required."});
        }
        const record = await ProgrammeType.findById(entityId);
        if (!record) {
            return res.status(404).json({message: "record not found."});
        }
        // Parse and increment the version number
        let currentVersion = parseFloat(record.version) || 0.0; // Default to 0.0 if version is not set
        let updatedVersion = (currentVersion + 0.1).toFixed(1); // Increment and format to one decimal place

        // Update the record status and version
        const updatedrecord = await ProgrammeType.findByIdAndUpdate(entityId, {
                status: "decline",
                reason: reason,
                version: updatedVersion
            }, {new: true} // Return the updated document
        );

        res.status(200).json({message: "record declined successfully."});
    } catch (error) {
        res.status(500).json({message: "Internal server error."});
    }
});
router.post("/modify", async (req, res) => {
    try {
        const {entityId, instructions} = req.body;

        if (!entityId || !instructions) {
            return res.status(400).json({message: "record ID and modification instructions are required."});
        }
        const record = await ProgrammeType.findById(entityId);
        if (!record) {
            return res.status(404).json({message: "record not found."});
        }
        // Parse and increment the version number
        let currentVersion = parseFloat(record.version) || 0.0; // Default to 0.0 if version is not set
        let updatedVersion = (currentVersion + 0.2).toFixed(1); // Increment and format to one decimal place

        // Update the record status and version
        const updatedrecord = await ProgrammeType.findByIdAndUpdate(entityId, {
                status: "modify",
                reason: instructions,
                version: updatedVersion
            }, {new: true} // Return the updated document
        );

        res.status(200).json({message: "record modification request submitted successfully."});
    } catch (error) {
        res.status(500).json({message: "Internal server error."});
    }
});

router.put('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = req.body;

        // Find the existing record
        let record = await ProgrammeType.findById(id);

        if (!record) {
            return res.status(404).json({ message: "Programme Type not found." });
        }

        // Update allowed fields
        if (updateData.type) record.type = updateData.type;
        if (updateData.description) record.description = updateData.description;
        if (updateData.status) record.status = updateData.status;

        // Increment version
        let currentVersion = parseFloat(record.version) || 0.0;
        record.version = (currentVersion + 0.1).toFixed(1);

        // Set status to 'pending' as the record has been modified
        record.status = "pending";

        // Save the updated record
        const updatedRecord = await record.save();

        // Create a task for the update
        const user = await User.findById(req.body.updated_by);
        const taskData = {
            refId: updatedRecord._id,
            type: 'Programme Type Update',
            title: `${user.name} updated Programme Type "${updatedRecord.type}"`,
            date: new Date(),
            user: user._id,
            ipAddress: req.ip,
        };
        await updateTaskStatus(taskData,taskData);

        res.status(200).json({
            message: "Programme Type updated successfully.",
            data: updatedRecord
        });
    } catch (error) {
        res.status(500).json({ message: "Internal server error.", error: error.message });
    }
});

module.exports = router;