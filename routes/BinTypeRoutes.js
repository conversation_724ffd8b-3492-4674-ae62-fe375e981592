const express = require("express");
const router = express.Router();
const controller = require("../controllers/BinTypeController");

// List and create
router.get("/", controller.listBinTypes);
router.post("/", controller.createBinType);

// Programme-specific list
router.get("/programme/:programmeTypeId", controller.listByProgramme);

// Single resource operations
router.get("/:id", controller.validateObjectId, controller.getBinTypeById);
router.put("/:id", controller.validateObjectId, controller.updateBinType);
router.patch("/:id/status", controller.validateObjectId, controller.patchStatus);
router.delete("/:id", controller.validateObjectId, controller.deleteBinType);

// Approval flows
router.post("/approve", controller.approve);
router.post("/decline", controller.decline);
router.post("/modify", controller.modify);

module.exports = router;

