const express = require("express");
const router = express.Router();
const controller = require("../controllers/itCardApiController");

// List cards
router.get("/", controller.listCards);

// Card actions (POST)
router.post("/:id/lock", controller.lockCard);
router.post("/:id/unlock", controller.unlockCard);
router.post("/:id/resign", controller.resignCard);
router.post("/:id/cancel-resign", controller.cancelResign);
router.post("/:id/replaceCard", controller.replaceCard);
router.post("/:id/activate", controller.activateCard);
router.post("/:id/changeName", controller.changeName);
router.post("/:id/restrict", controller.restrict);
router.post("/:id/restrictFraud", controller.restrictFraud);
router.post("/:id/restrictStolen", controller.restrictStolen);
router.post("/:id/restrictLost", controller.restrictLost);
router.post("/:id/forcePinLock", controller.forcePinLock);
router.post("/:id/restrictMobileLost", controller.restrictMobileLost);
router.post("/:id/change-3d-ans", controller.change3dAns);
router.post("/:id/change-3d-phone", controller.change3dPhone);
router.post("/:id/change-nickname", controller.changeNickname);
router.post("/:id/set-limit", controller.setLimit);

// Card actions (other methods)
router.get("/:id/status", controller.getStatus);
router.get("/:id/limits", controller.getLimits);
router.post("/:id/patch-card-limits", controller.patchCardLimits);
router.post("/:id/close", controller.closeCard);
router.get("/:id/resetPinTries", controller.resetPinTries);
router.get("/:id/associatedClients", controller.associatedClients);
router.get("/:id/embossInfo", controller.embossInfo);
router.get("/:id/actionLog", controller.actionLog);
router.get("/:id/transactions", controller.transactions);
router.get("/:id/card-number", controller.cardNumber);

module.exports = router;