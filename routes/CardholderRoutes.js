const express = require("express");
const router = express.Router();
const controller = require("../controllers/cardholderController");
const { body, validationResult } = require("express-validator");

router.post(
    '/:id/updatePermissions',
    [
        body('permissions').isArray().withMessage('Permissions must be an array'),
        body('permissionAudit').isArray().withMessage('Permission audit must be an array'),
    ],
    (req, res, next) => {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        return controller.updatePermissions(req, res, next);
    }
);

module.exports = router;