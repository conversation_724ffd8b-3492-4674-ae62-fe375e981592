const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const controller = require('../controllers/cardImagesController');

const UPLOAD_DIR = path.join(__dirname, '..', 'uploads');
const storage = multer.diskStorage({
  destination: (req, file, cb) => cb(null, UPLOAD_DIR),
  filename: (req, file, cb) => {
    const ext = path.extname(file.originalname) || '';
    const timestamp = Date.now();
    const safeName = file.fieldname.replace(/\s+/g, '_');
    cb(null, `${safeName}-${timestamp}${ext}`);
  }
});

const fileFilter = (req, file, cb) => {
  if (/^image\/(png|jpe?g|webp|gif)$/.test(file.mimetype)) return cb(null, true);
  return cb(new Error('Unsupported file type'), false);
};

const upload = multer({
  storage,
  fileFilter,
  limits: { fileSize: 5 * 1024 * 1024 } // 5MB
});

const uploadFields = upload.fields([
  { name: 'front_side', maxCount: 1 },
  { name: 'back_side', maxCount: 1 }
]);

// upload (front_side/back_side)
router.post('/', uploadFields, controller.uploadImages);

// list
router.get('/', controller.listImages);

// delete
router.delete('/:id', controller.deleteImage);

module.exports = router;
