const express = require('express');
const router = express.Router();
const controller = require('../controllers/binUsageController');

// Create
router.post('/', controller.createBinUsage);

// List
router.get('/', controller.listBinUsages);

// Soft delete
router.delete('/:id', controller.softDeleteBinUsage);

// Approvals / workflow
router.post('/approve', controller.approveBinUsage);
router.post('/decline', controller.declineBinUsage);
router.post('/modify', controller.modifyBinUsage);

// Update
router.put('/:id', controller.updateBinUsage);

module.exports = router;