const express = require("express")
const zoneMethodConfigController = require("../controllers/zoneMethodConfigController")
const router = express.Router()

// GET all configurations
router.get("/", zoneMethodConfigController.getAllConfigs)

// GET configurations for a specific zone
router.get("/zone/:zoneId", zoneMethodConfigController.getConfigsByZone)

// GET configurations for a specific country
router.get("/country/:country", zoneMethodConfigController.getConfigsByCountry)

// GET a specific configuration by ID
router.get("/:id", zoneMethodConfigController.getConfigById)

// POST a new configuration
router.post("/", zoneMethodConfigController.createConfig)

// PUT (update) a configuration
router.put("/:id", zoneMethodConfigController.updateConfig)

// DELETE a configuration
router.delete("/:id", zoneMethodConfigController.deleteConfig)

module.exports = router
