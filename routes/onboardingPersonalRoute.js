const express = require("express");
const router = express.Router();
const {
    createIndividualOnboarding, getAllIndividualOnboardings, getIndividualOnboardingById,
    activateAccount, listOfAccounts
} = require("../controllers/onboardingPersonalController");
// // POST route to create a new individual onboarding application
router.post('/', createIndividualOnboarding);

router.post('/activate',  activateAccount);




// GET route to retrieve all onboarding applications

router.get('/accounts', listOfAccounts);



// // GET route to retrieve all onboarding applications
router.get('/', getAllIndividualOnboardings);

// // GET route to retrieve a specific onboarding application by ID
router.get('/:id', getIndividualOnboardingById);


module.exports = router;