const express = require('express');
const router = express.Router();
const controller = require('../controllers/ClientController');

// Create account
router.post('/createAccount', controller.createAccount);

// Create virtual card
router.post('/createCard/virtual', controller.createVirtualCard);

// Create physical card
router.post('/createCard/physical', controller.createPhysicalCard);

// Get card details
router.get('/card/:id', controller.getCardById);

// Get client info
router.get('/:id', controller.getClient);

// Add or update address
router.post('/addAddress', controller.addAddress);

module.exports = router;
