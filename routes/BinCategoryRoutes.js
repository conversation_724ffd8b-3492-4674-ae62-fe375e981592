const express = require('express');
const router = express.Router();
const controller = require('../controllers/binCategoryController');

// Create
router.post('/', controller.createBinCategory);

// List
router.get('/', controller.listBinCategories);

// Soft delete
router.delete('/:id', controller.deleteBinCategory);

// Approve / Decline / Modify
router.post('/approve', controller.approveBinCategory);
router.post('/decline', controller.declineBinCategory);
router.post('/modify', controller.modifyBinCategory);

// Update
router.put('/:id', controller.updateBinCategory);

module.exports = router;