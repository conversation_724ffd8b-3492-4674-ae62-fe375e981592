const express = require("express");
const router = express.Router();
const controller = require("../controllers/companyContactController");

// CREATE a new contact
router.post("/", controller.createContact);

// GET all contacts
router.get("/", controller.listContacts);

// GET a single contact by ID
router.get("/:id", controller.getContactById);

// UPDATE a contact by ID
router.put("/:id", controller.updateContact);

// DELETE a contact by ID
router.delete("/:id", controller.deleteContact);

module.exports = router;
