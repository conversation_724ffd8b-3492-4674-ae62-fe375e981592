// routes/roleRoutes.js
const express = require('express');
const router = express.Router();
const Role = require('../models/Role');

// ✅ Create a new role
router.post('/', async (req, res) => {
    try {
        const { name, description, permissions, dashboard } = req.body;

        if (!name) return res.status(400).json({ error: 'Role name is required' });

        const role = new Role({ name, description, permissions, dashboard, deleted_at: null });
        await role.save();

        res.status(201).json(role);
    } catch (err) {
        res.status(500).json({ error: 'Internal Server Error' });
    }
});

// ✅ Get all active roles
router.get('/', async (req, res) => {
    try {
        const roles = await Role.find({ deleted_at: null });
        res.status(200).json(roles);
    } catch (err) {
        res.status(500).json({ error: 'Internal Server Error' });
    }
});

// ✅ Get a single active role by ID
router.get('/:id', async (req, res) => {
    try {
        const role = await Role.findOne({ _id: req.params.id, deleted_at: null });
        if (!role) return res.status(404).json({ error: 'Role not found' });

        res.status(200).json(role);
    } catch (err) {
        res.status(500).json({ error: 'Internal Server Error' });
    }
});

// ✅ Update an active role by ID
router.put('/:id', async (req, res) => {
    try {
        const { name, description, permissions, dashboard } = req.body;

        const updatedRole = await Role.findOneAndUpdate(
            { _id: req.params.id, deleted_at: null },
            { name, description, permissions, dashboard },
            { new: true, runValidators: true }
        );

        if (!updatedRole) return res.status(404).json({ error: 'Role not found' });

        res.status(200).json(updatedRole);
    } catch (err) {
        res.status(500).json({ error: 'Internal Server Error' });
    }
});

// ✅ Soft delete a role by ID
router.delete('/:id', async (req, res) => {
    try {
        const deletedRole = await Role.findOneAndUpdate(
            { _id: req.params.id, deleted_at: null },
            { deleted_at: new Date() },
            { new: true }
        );

        if (!deletedRole) return res.status(404).json({ error: 'Role not found or already deleted' });

        res.status(200).json({ message: 'Role soft deleted successfully' });
    } catch (err) {
        res.status(500).json({ error: 'Internal Server Error' });
    }
});

module.exports = router;
