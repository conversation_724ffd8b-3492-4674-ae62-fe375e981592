// routes/userRoutes.js
const express = require('express');
const router = express.Router();
const { assignRoleToUser, checkUserPermission, loginUser, registerUser } = require('../controllers/userController');
const authenticateToken = require('../middleware/authMiddleware');
const { body, validationResult } = require('express-validator');
const User = require('../models/user');
const CompanyModal = require('../models/company');
const B2BAccount = require('../models/B2BAccount');
const speakeasy = require("speakeasy")
const QRCode = require("qrcode")
const bcrypt = require("bcrypt")
const IndividualOnboarding = require("../models/IndividualOnboarding");

// GET /api/users
router.get('/', async (req, res) => {
    try {
        const users = await User.find().populate("company").select('-password').populate("roles"); // Exclude password from the result
        res.json(users);
    } catch (error) {
        res.status(500).json({ message: 'Internal Server Error' });
    }
});



// Edit user details
router.put('/:id', async (req, res) => {
    const { id } = req.params;
    const { name, email, roles } = req.body;

    try {
        const updatedUser = await User.findByIdAndUpdate(
            id,
            { name, email, roles },
            { new: true } // Return the updated user after modification
        );

        if (!updatedUser) {
            return res.status(404).json({ message: 'User not found' });
        }

        res.json(updatedUser);
    } catch (error) {
        res.status(500).json({ message: 'Error updating user', error });
    }
});


// Delete user by ID
router.delete('/:id', async (req, res) => {
    const { id } = req.params;

    try {
        const deletedUser = await User.findByIdAndDelete(id);

        if (!deletedUser) {
            return res.status(404).json({ message: 'User not found' });
        }

        res.json({ message: 'User deleted successfully' });
    } catch (error) {
        res.status(500).json({ message: 'Error deleting user', error });
    }
});
// GET route to retrieve a single user by ID
router.get('/user/:id', async (req, res) => {
    const { id } = req.params;

    try {
        // Find the user by ID
        const user = await User.findById(id);

        // Check if user exists
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Return the user data
        res.json(user);
    } catch (error) {
        res.status(500).json({ message: 'Error retrieving user', error });
    }
});

// Route to assign a role to a user
router.post('/assign-role', async (req, res) => {
    const { userId, roleId } = req.body;
    await assignRoleToUser(userId, roleId);
    res.status(200).send('Role assigned successfully');
});

// Route to check user permission
router.get('/check-permission', async (req, res) => {
    const { userId, permission } = req.query;
    const hasPermission = await checkUserPermission(userId, permission);
    res.status(200).send({ hasPermission });
});

// Get the authenticated user's information
router.get('/me', async (req, res) => {
    try {
        const id = req.user.id; // Assuming req.user.id contains the user's email or ID
        const user = await User.findById(id).select('-password').populate("roles").lean(); // Use .lean() to return a plain JavaScript object

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        if(user.dashboard === "cardholder"){
       const  cardholder = await IndividualOnboarding.findById(user.recordId);
       return   res.json({cardholder, ...user});
        }

        if(user.dashboard === "programmeManager"){
       const  pm = await CompanyModal.findById(user.recordId);
       return   res.json({pm, ...user});
        }

        if(user.dashboard === "corporate"){
       const  corporate = await B2BAccount.findById(user.recordId);
       return   res.json({corporate, ...user});
        }

     return    res.json(user); // Respond with the user object
    } catch (error) {
        res.status(500).json({ message: 'Server error' });
    }
});
// Route for user login

router.post(
    '/register',
    [
        body('name').notEmpty().withMessage('Name is required'),
        body('email').isEmail().withMessage('Email is invalid'),
        body('status').notEmpty().withMessage('Status is required'),
    ],
    async (req, res) => {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }

        const { name, email,  roles ,status, dashboard,recordId,company} = req.body;


        try {
            const newUser = await registerUser(name, email, roles,status,dashboard,recordId,company);
            res.status(201).json({ message: 'User registered successfully', user: newUser });
        } catch (error) {
            res.status(400).json({ message: error.message });
        }
    }
);
router.get('checkEmail/:email', async (req, res) => {
    try {
        const {email} = req.params;

        // Check if email exists
        const existingUser = await User.findOne({email});
        if (existingUser) {
            return res.status(400).json({message: "Email already exists"});
        }

        res.json({message: "Email available"});
    } catch (error) {
        return res.status(500).json({message: "Server error", error: error.message});
    }
});
// Get 2FA status
router.get("/2fa/status", authenticateToken, async (req, res) => {
    try {
        const user = await User.findById(req.user.id)

        if (!user) {
            return res.status(404).json({
                success: false,
                error: "User not found",
            })
        }

        return res.json({
            success: true,
            data: {
                enabled: !!user.twoFactorEnabled,
            },
        })
    } catch (error) {
        return res.status(500).json({
            success: false,
            error: "Server error",
        })
    }
})

// Generate 2FA secret
router.post("/2fa/generate", authenticateToken, async (req, res) => {
    try {
        const user = await User.findById(req.user.id)

        if (!user) {
            return res.status(404).json({
                success: false,
                error: "User not found",
            })
        }

        // Generate a new secret
        const secret = speakeasy.generateSecret({
            length: 20,
            name: `Ryvyl:${user.email}`,
        })

        // Store the secret temporarily (not enabled yet)
        // Only update the twoFactorSecret field, don't modify other fields
        await User.findByIdAndUpdate(
            user._id,
            { $set: { twoFactorSecret: secret.base32 } },
            { new: true, runValidators: false },
        )

        // Generate QR code
        const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url)

        return res.json({
            success: true,
            data: {
                secret: secret.base32,
                qrCodeUrl,
            },
        })
    } catch (error) {
        return res.status(500).json({
            success: false,
            error: "Server error",
        })
    }
})

// Verify and enable 2FA
router.post("/2fa/verify", authenticateToken, async (req, res) => {
    try {
        const { code } = req.body

        if (!code) {
            return res.status(400).json({
                success: false,
                error: "Verification code is required",
            })
        }

        const user = await User.findById(req.user.id)

        if (!user || !user.twoFactorSecret) {
            return res.status(400).json({
                success: false,
                error: "No 2FA secret found. Please restart the setup process.",
            })
        }

        // Verify the code
        const verified = speakeasy.totp.verify({
            secret: user.twoFactorSecret,
            encoding: "base32",
            token: code,
        })

        if (!verified) {
            return res.status(400).json({
                success: false,
                error: "Invalid verification code",
            })
        }

        // Enable 2FA using findByIdAndUpdate
        await User.findByIdAndUpdate(user._id, { $set: { twoFactorEnabled: true } }, { new: true, runValidators: false })

        return res.json({ success: true })
    } catch (error) {
        return res.status(500).json({
            success: false,
            error: "Server error",
        })
    }
})

// Disable 2FA
router.post("/2fa/disable", authenticateToken, async (req, res) => {
    try {
        const user = await User.findById(req.user.id)

        if (!user) {
            return res.status(404).json({
                success: false,
                error: "User not found",
            })
        }

        // Disable 2FA using findByIdAndUpdate
        await User.findByIdAndUpdate(
            user._id,
            { $set: { twoFactorEnabled: false, twoFactorSecret: null } },
            { new: true, runValidators: false },
        )

        return res.json({ success: true })
    } catch (error) {
        return res.status(500).json({
            success: false,
            error: "Server error",
        })
    }
})

// Change password
router.post("/password/change", authenticateToken, async (req, res) => {
    try {
        const { currentPassword, newPassword } = req.body

        if (!currentPassword || !newPassword) {
            return res.status(400).json({
                success: false,
                error: "Current password and new password are required",
            })
        }

        if (newPassword.length < 8) {
            return res.status(400).json({
                success: false,
                error: "New password must be at least 8 characters long",
            })
        }

        const user = await User.findById(req.user.id)

        if (!user) {
            return res.status(404).json({
                success: false,
                error: "User not found",
            })
        }

        // Verify current password
        const isMatch = await bcrypt.compare(currentPassword, user.password)

        if (!isMatch) {
            return res.status(400).json({
                success: false,
                error: "Current password is incorrect",
            })
        }

        // Check password strength
        const passwordStrength = checkPasswordStrength(newPassword)
        if (passwordStrength < 40) {
            return res.status(400).json({
                success: false,
                error: "Password is too weak. Please include uppercase, lowercase, numbers, and special characters.",
            })
        }

        // Hash the new password
        const salt = await bcrypt.genSalt(10)
        const hashedPassword = await bcrypt.hash(newPassword, salt)

        // Update password using findByIdAndUpdate to avoid validation issues
        await User.findByIdAndUpdate(user._id, { $set: { password: hashedPassword } }, { new: true, runValidators: false })

        return res.json({ success: true })
    } catch (error) {
        return res.status(500).json({
            success: false,
            error: "Server error",
        })
    }
})


function checkPasswordStrength(password) {
    let score = 0

    // Length contribution (up to 25 points)
    score += Math.min(password.length * 2, 25)

    // Character variety contribution
    if (/[A-Z]/.test(password)) score += 15 // uppercase
    if (/[a-z]/.test(password)) score += 15 // lowercase
    if (/[0-9]/.test(password)) score += 15 // digits
    if (/[^A-Za-z0-9]/.test(password)) score += 15 // special chars

    // Complexity patterns
    if (/[A-Z].*[A-Z]/.test(password)) score += 5 // multiple uppercase
    if (/[a-z].*[a-z]/.test(password)) score += 5 // multiple lowercase
    if (/[0-9].*[0-9]/.test(password)) score += 5 // multiple digits
    if (/[^A-Za-z0-9].*[^A-Za-z0-9]/.test(password)) score += 5 // multiple special

    // Penalize for patterns
    if (/^[A-Za-z]+$/.test(password)) score -= 10 // letters only
    if (/^[0-9]+$/.test(password)) score -= 15 // numbers only
    if (/^[A-Za-z0-9]+$/.test(password)) score -= 5 // alphanumeric only

    // Ensure score is between 0-100
    return Math.max(0, Math.min(100, score))
}

module.exports = router;
