const axios = require("axios");
const WebhookLog = require("../models/WebhookLog");
const { generateJwtToken } = require("./jwtService"); // <-- use shared token generator



async function sendCardCreationWebhook(cardData) {
    const webhookUrl = process.env.CARD_WEBHOOK;
    const payload = { event: "card_created", cardData };

    let responseStatus = null;
    let responseBody = null;
    let errorData = null;

    try {
        const token = generateJwtToken();

        const response = await axios.post(webhookUrl, payload, {
            headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${token}`
            }
        });

        responseStatus = response.status;
        responseBody = response.data;
    } catch (error) {
        responseStatus = error.response?.status || 500;
        errorData = {
            message: error.message,
            data: error.response?.data || null
        };
    }

    // Log webhook attempt to MongoDB
    try {
        await WebhookLog.create({
            event: "card_created",
            webhookUrl,
            payloadSent: payload,
            responseStatus,
            responseBody,
            error: errorData
        });
    } catch (logError) {
        console.error("Failed to log webhook attempt:", logError);
    }
}

module.exports = sendCardCreationWebhook;
