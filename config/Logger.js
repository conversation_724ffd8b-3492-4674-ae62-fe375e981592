// utils/logger.js
const Log = require('../models/Log'); // Adjust the path as needed

async function logRequest(config) {
    config.metadata = { startTime: new Date() };

    let parsedRequestBody = {};
    try {
        parsedRequestBody = typeof config.data === 'string' ? JSON.parse(config.data) : config.data;
    } catch {
        parsedRequestBody = {};
    }x

    config._logMeta = {
        method: config.method?.toUpperCase() || 'UNKNOWN',
        url: config.url || 'UNKNOWN',
        requestBody: parsedRequestBody,
        queryParams: config.params || {},
        timestamp: new Date(),
    };

    return config;
}

async function logResponse(response) {
    const { config } = response;
    const responseTime = `${new Date() - config.metadata.startTime}ms`;

    try {
        const logEntry = new Log({
            remoteAddr: 'server',
            userIdentity: 'system',
            timestamp: config._logMeta.timestamp,
            method: config._logMeta.method,
            url: config._logMeta.url,
            httpVersion: 'HTTP/1.1',
            status: response.status,
            responseSize: Buffer.byteLength(JSON.stringify(response.data), 'utf8') + ' bytes',
            referer: 'internal-service',
            userAgent: 'axios-client',
            responseTime,
            requestBody: config._logMeta.requestBody,
            queryParams: config._logMeta.queryParams,
            responseBody: response.data,
        });

        await logEntry.save();
    } catch (err) {}

    return response;
}

async function logError(error) {
    const config = error.config || {};
    const responseTime = config.metadata ? `${new Date() - config.metadata.startTime}ms` : 'N/A';

    try {
        const logEntry = new Log({
            remoteAddr: 'server',
            userIdentity: 'system',
            timestamp: config._logMeta?.timestamp || new Date(),
            method: config._logMeta?.method || 'UNKNOWN',
            url: config._logMeta?.url || 'UNKNOWN',
            httpVersion: 'HTTP/1.1',
            status: error.response?.status || 500,
            responseSize: error.response
                ? Buffer.byteLength(JSON.stringify(error.response.data), 'utf8') + ' bytes'
                : '0 bytes',
            referer: 'internal-service',
            userAgent: 'axios-client',
            responseTime,
            requestBody: config._logMeta?.requestBody || {},
            queryParams: config._logMeta?.queryParams || {},
            responseBody: error.response?.data || { error: error.message },
        });

        await logEntry.save();
    } catch (err) {}

    return Promise.reject(error);
}

module.exports = {
    logRequest,
    logResponse,
    logError,
};
