const fs = require("fs")
const path = require("path")
const { promisify } = require("util")
const writeFileAsync = promisify(fs.writeFile)
const mkdirAsync = promisify(fs.mkdir)
const IndividualOnboarding = require("../models/IndividualOnboarding") // Adjust path as needed

// Configuration for file uploads
const UPLOAD_CONFIG = {
    MAX_FILE_SIZE: 2 * 1024 * 1024, // 5MB
    ALLOWED_MIME_TYPES: ["image/jpeg", "image/png", "image/jpg", "application/pdf"],
    UPLOAD_DIR: path.join(__dirname, "../uploads/id-documents"),
}

/**
 * Validates an uploaded file
 * @param {Object} file - The file object
 * @returns {Object} - Validation result with status and message
 */
const validateFile = (file) => {
    // Check if file exists
    if (!file) {
        return { valid: false, message: "No file provided" }
    }

    // Check file size
    if (file.size > UPLOAD_CONFIG.MAX_FILE_SIZE) {
        return {
            valid: false,
            message: `File size exceeds the maximum limit of ${UPLOAD_CONFIG.MAX_FILE_SIZE / (1024 * 1024)}MB`,
        }
    }

    // Check file type
    if (!UPLOAD_CONFIG.ALLOWED_MIME_TYPES.includes(file.mimetype)) {
        return {
            valid: false,
            message: `Invalid file type. Allowed types: ${UPLOAD_CONFIG.ALLOWED_MIME_TYPES.join(", ")}`,
        }
    }

    return { valid: true }
}

/**
 * Saves a file to the file system
 * @param {Object} file - The file object
 * @param {String} clientId - The client ID for folder naming
 * @param {String} side - 'front' or 'back'
 * @returns {String} - The file path
 */
const saveFile = async (file, clientId, side) => {
    try {
        // Create directory if it doesn't exist
        const clientDir = path.join(UPLOAD_CONFIG.UPLOAD_DIR, clientId)
        await mkdirAsync(clientDir, { recursive: true })

        // Generate unique filename
        const timestamp = Date.now()
        const fileExtension = path.extname(file.originalname || "")
        const filename = `id_${side}_${timestamp}${fileExtension}`
        const filePath = path.join(clientDir, filename)

        // Save file
        if (file.buffer) {
            // For multer-style uploads
            await writeFileAsync(filePath, file.buffer)
        } else if (file.path) {
            // For temporary files
            const readStream = fs.createReadStream(file.path)
            const writeStream = fs.createWriteStream(filePath)
            readStream.pipe(writeStream)

            await new Promise((resolve, reject) => {
                writeStream.on("finish", resolve)
                writeStream.on("error", reject)
            })
        } else if (file.base64) {
            // For base64 encoded files
            const base64Data = file.base64.split(";base64,").pop()
            await writeFileAsync(filePath, base64Data, { encoding: "base64" })
        } else {
            throw new Error("Unsupported file format")
        }

        // Return relative path for storage in DB
        return `/uploads/id-documents/${clientId}/${filename}`
    } catch (error) {
        throw error
    }
}

/**
 * Process base64 encoded image
 * @param {String} base64String - Base64 encoded image
 * @returns {Object} - File object with buffer and mimetype
 */
const processBase64Image = (base64String) => {
    if (!base64String) return null

    try {
        // Extract mime type and base64 data
        const matches = base64String.match(/^data:([A-Za-z-+/]+);base64,(.+)$/)

        if (!matches || matches.length !== 3) {
            throw new Error("Invalid base64 string format")
        }

        const mimetype = matches[1]
        const buffer = Buffer.from(matches[2], "base64")

        return {
            buffer,
            mimetype,
            size: buffer.length,
            base64: base64String,
        }
    } catch (error) {
        throw error
    }
}

/**
 * Updates the ID document images for a client
 * @param {String} clientCode - The client code
 * @param {Object} idFront - Front ID image (file or base64)
 * @param {Object} idBack - Back ID image (file or base64)
 * @returns {Object} - Updated onboarding document
 */
const updateIdDocumentImages = async (clientCode, idFront, idBack) => {
    try {
        // Find the client by clientCode
        const onboardingData = await IndividualOnboarding.findOne({ clientID: clientCode })

        if (!onboardingData) {
            throw new Error(`Client with code ${clientCode} not found`)
        }

        // Process and validate files
        let frontFile = idFront[0]
        let backFile = idBack[0]

        // Handle base64 encoded images
        if (typeof idFront === "string") {
            frontFile = processBase64Image(idFront)
        }

        if (typeof idBack === "string") {
            backFile = processBase64Image(idBack)
        }

        // Validate files
        const frontValidation = validateFile(frontFile)
        if (!frontValidation.valid) {
            throw new Error(`Front ID validation failed: ${frontValidation.message}`)
        }

        if (backFile) {
            const backValidation = validateFile(backFile)
            if (!backValidation.valid) {
                throw new Error(`Back ID validation failed: ${backValidation.message}`)
            }
        }

        // Save files
        const frontImagePath = await saveFile(frontFile, clientCode, "front")
        let backImagePath = null

        if (backFile) {
            backImagePath = await saveFile(backFile, clientCode, "back")
        }

        // Update onboarding document
        if (!onboardingData.idDocument) {
            onboardingData.idDocument = {}
        }

        onboardingData.idDocument.frontImagePath = frontImagePath

        if (backImagePath) {
            onboardingData.idDocument.backImagePath = backImagePath
        }

        // Save the updated document
        await onboardingData.save()

        return onboardingData
    } catch (error) {
        throw error
    }
}

module.exports = {
    updateIdDocumentImages,
    validateFile,
    processBase64Image,
}

