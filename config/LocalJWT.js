const jwt = require("jsonwebtoken");

const JWT_SECRET = process.env.JWT_SECRET;
const REFRESH_SECRET = process.env.REFRESH_SECRET || "refresh-secret"; // should be different from JWT_SECRET

// Access token = short-lived (1d in your case)
function generateAccessToken(payload, expiresIn = "1d") {
    return jwt.sign(payload, JWT_SECRET, { expiresIn });
}

// Refresh token = longer-lived (7d or more)
function generateRefreshToken(payload, expiresIn = "7d") {
    return jwt.sign(payload, REFRESH_SECRET, { expiresIn });
}

function verifyAccessToken(token) {
    try {
        return jwt.verify(token, JWT_SECRET);
    } catch (err) {
        return null;
    }
}

function verifyRefreshToken(token) {
    try {
        return jwt.verify(token, REFRESH_SECRET);
    } catch (err) {
        return null;
    }
}

function setAuthCookie(res, token, maxAgeMs = 24 * 60 * 60 * 1000) {
    res.cookie("token", token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: maxAgeMs,
    });
}


// Middleware to verify
function  verifyToken(req, res, next)  {
    const authHeader = req.headers["authorization"];
    const token = authHeader && authHeader.split(" ")[1]; // Bearer <token>

    if (!token) return res.status(401).json({ message: "No token provided" });

    jwt.verify(token, JWT_SECRET, (err, decoded) => {
        if (err) return res.status(403).json({ message: "Invalid token" });
        req.user = decoded; // { id, email }
        next();
    });
}

module.exports = {
    generateAccessToken,
    generateRefreshToken,
    verifyAccessToken,
    verifyRefreshToken,
    setAuthCookie, verifyToken
};
