require("dotenv").config();
const https = require("https");
const axios = require("axios");
const Log = require("../models/Log"); // Adjust this path if needed

// Create HTTPS agent using client certificate
function createHttpsAgent() {
  const certFile = process.env.IT_CARD_CERT;
  const keyFile = process.env.IT_CARD_KEY;

  if (!certFile || !keyFile) {
    return null;
  }

  try {
    return new https.Agent({
      cert: certFile,
      key: keyFile,
    });
  } catch (err) {
    throw err;
  }
}

// Axios instance with HTTPS agent
const httpsAgent = createHttpsAgent();
const axiosInstance = axios.create({
  ...(httpsAgent ? { httpsAgent } : {}),
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor
axiosInstance.interceptors.request.use(
  (config) => {
    config.metadata = { startTime: new Date() };

    let parsedRequestBody = {};
    try {
      parsedRequestBody =
        typeof config.data === "string" ? JSON.parse(config.data) : config.data;
    } catch (e) {
      console.log("Failed to parse request body for logging:", e.message);
      parsedRequestBody = {}; // Fallback if parse fails
    }

    // Also capture query params if exist (for GET requests)
    let queryParams = config.params || {};

    config._logMeta = {
      method: config.method?.toUpperCase() || "UNKNOWN",
      url: config.url || "UNKNOWN",
      requestBody: parsedRequestBody || {},
      queryParams: queryParams || {},
      timestamp: new Date(),
    };

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
axiosInstance.interceptors.response.use(
  async (response) => {
    const { config } = response;
    const responseTime = `${new Date() - config.metadata.startTime}ms`;

    try {
      const logEntry = new Log({
        remoteAddr: "server",
        userIdentity: "system",
        timestamp: config._logMeta.timestamp,
        method: config._logMeta.method,
        url: config._logMeta.url,
        httpVersion: "HTTP/1.1",
        status: response.status,
        responseSize:
          Buffer.byteLength(JSON.stringify(response.data), "utf8") + " bytes",
        referer: "internal-service",
        userAgent: "axios-client",
        responseTime,
        requestBody: config._logMeta.requestBody,
        queryParams: config._logMeta.queryParams,
        responseBody: response.data,
      });

      await logEntry.save();
    } catch (err) {
      console.log("Failed to log API request/response:", err.message);
    }

    return response;
  },
  async (error) => {
    const config = error.config || {};
    const responseTime = config.metadata
      ? `${new Date() - config.metadata.startTime}ms`
      : "N/A";

    try {
      const logEntry = new Log({
        remoteAddr: "server",
        userIdentity: "system",
        timestamp: config._logMeta?.timestamp || new Date(),
        method: config._logMeta?.method || "UNKNOWN",
        url: config._logMeta?.url || "UNKNOWN",
        httpVersion: "HTTP/1.1",
        status: error.response?.status || 500,
        responseSize: error.response
          ? Buffer.byteLength(JSON.stringify(error.response.data), "utf8") +
            " bytes"
          : "0 bytes",
        referer: "internal-service",
        userAgent: "axios-client",
        responseTime,
        requestBody: config._logMeta?.requestBody || {},
        queryParams: config._logMeta?.queryParams || {},
        responseBody: error.response?.data || { error: error.message },
      });

      await logEntry.save();
    } catch (err) {
      console.log("Failed to log API error request/response:", err.message);
    }

    return Promise.reject(error);
  }
);

// Helper functions
async function sendPostRequest(url, data, headers = {}) {
  try {
    const response = await axiosInstance.post(url, data, { headers });
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
}

async function sendGetRequest(url, headers = {}, params = {}) {
  try {
    const response = await axiosInstance.get(url, { headers, params });
    return response.data;
  } catch (error) {
    throw error;
  }
}

async function sendPutRequest(url, data, headers = {}) {
  try {
    const response = await axiosInstance.put(url, data, { headers });
    return response.data;
  } catch (error) {
    throw error;
  }
}

async function sendPatchRequest(url, data, headers = {}) {
  try {
    const response = await axiosInstance.patch(url, data, { headers });
    return response.data;
  } catch (error) {
    throw error;
  }
}

module.exports = {
  sendPostRequest,
  sendGetRequest,
  sendPutRequest,
  sendPatchRequest,
};
