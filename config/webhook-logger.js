import express from 'express';
import mongoose from 'mongoose';
import axios from 'axios';
import crypto from 'crypto';

// MongoDB connection
const connectDB = async () => {
    try {
        await mongoose.connect('mongodb://localhost:27017/webhook_logs', {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });
        console.log('✅ MongoDB connected successfully');
    } catch (error) {
        console.error('❌ MongoDB connection error:', error.message);
    }
};

// Webhook Log Schema
const webhookLogSchema = new mongoose.Schema({
    // Request Information
    webhookId: {
        type: String,
        required: true,
        unique: true,
        default: () => crypto.randomUUID()
    },
    triggerEvent: {
        type: String,
        required: true, // e.g., 'create_card', 'update_card', etc.
    },
    webhookUrl: {
        type: String,
        required: true
    },

    // Request Details
    requestPayload: {
        type: mongoose.Schema.Types.Mixed,
        required: true
    },
    requestHeaders: {
        type: Object,
        default: {}
    },
    requestMethod: {
        type: String,
        default: 'POST'
    },

    // Response Details
    responseStatus: {
        type: Number,
        default: null
    },
    responseBody: {
        type: mongoose.Schema.Types.Mixed,
        default: null
    },
    responseHeaders: {
        type: Object,
        default: {}
    },

    // Timing Information
    requestStartTime: {
        type: Date,
        default: Date.now
    },
    requestEndTime: {
        type: Date,
        default: null
    },
    responseTime: {
        type: Number, // in milliseconds
        default: null
    },

    // Status and Error Handling
    status: {
        type: String,
        enum: ['pending', 'success', 'failed', 'timeout'],
        default: 'pending'
    },
    errorMessage: {
        type: String,
        default: null
    },
    retryCount: {
        type: Number,
        default: 0
    },
    maxRetries: {
        type: Number,
        default: 3
    },

    // Additional Metadata
    userAgent: String,
    ipAddress: String,
    cardId: String, // specific to your create card function
    userId: String,

    // Timestamps
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
}, {
    timestamps: true
});

// Add indexes for better query performance
webhookLogSchema.index({ webhookId: 1 });
webhookLogSchema.index({ triggerEvent: 1 });
webhookLogSchema.index({ status: 1 });
webhookLogSchema.index({ createdAt: -1 });
webhookLogSchema.index({ cardId: 1 });

const WebhookLog = mongoose.model('WebhookLog', webhookLogSchema);

// Webhook Service Class
class WebhookService {
    constructor() {
        this.defaultTimeout = 30000; // 30 seconds
        this.defaultRetries = 3;
    }

    async sendWebhook(options) {
        const {
            url,
            payload,
            triggerEvent,
            cardId,
            userId,
            headers = {},
            timeout = this.defaultTimeout,
            maxRetries = this.defaultRetries
        } = options;

        // Create initial log entry
        const webhookLog = new WebhookLog({
            triggerEvent,
            webhookUrl: url,
            requestPayload: payload,
            requestHeaders: {
                'Content-Type': 'application/json',
                'User-Agent': 'Webhook-Service/1.0',
                ...headers
            },
            cardId,
            userId,
            maxRetries,
            requestStartTime: new Date()
        });

        try {
            await webhookLog.save();
            console.log(`📤 Webhook log created: ${webhookLog.webhookId}`);

            // Send the webhook with retry logic
            const result = await this.sendWithRetry(webhookLog, url, payload, headers, timeout);
            return result;

        } catch (error) {
            console.error('❌ Error creating webhook log:', error.message);
            throw error;
        }
    }

    async sendWithRetry(webhookLog, url, payload, headers, timeout) {
        let lastError = null;

        for (let attempt = 0; attempt <= webhookLog.maxRetries; attempt++) {
            try {
                console.log(`🔄 Webhook attempt ${attempt + 1}/${webhookLog.maxRetries + 1} for ${webhookLog.webhookId}`);

                const startTime = Date.now();

                const response = await axios({
                    method: 'POST',
                    url: url,
                    data: payload,
                    headers: {
                        'Content-Type': 'application/json',
                        'User-Agent': 'Webhook-Service/1.0',
                        'X-Webhook-ID': webhookLog.webhookId,
                        'X-Webhook-Event': webhookLog.triggerEvent,
                        ...headers
                    },
                    timeout: timeout,
                    validateStatus: () => true // Don't throw on HTTP error status
                });

                const endTime = Date.now();
                const responseTime = endTime - startTime;

                // Update log with response details
                await this.updateWebhookLog(webhookLog._id, {
                    responseStatus: response.status,
                    responseBody: response.data,
                    responseHeaders: response.headers,
                    requestEndTime: new Date(endTime),
                    responseTime: responseTime,
                    retryCount: attempt,
                    status: response.status >= 200 && response.status < 300 ? 'success' : 'failed',
                    errorMessage: response.status >= 400 ? `HTTP ${response.status}: ${response.statusText}` : null
                });

                if (response.status >= 200 && response.status < 300) {
                    console.log(`✅ Webhook successful: ${webhookLog.webhookId} (${responseTime}ms)`);
                    return {
                        success: true,
                        webhookId: webhookLog.webhookId,
                        status: response.status,
                        responseTime: responseTime,
                        attempt: attempt + 1
                    };
                } else {
                    lastError = new Error(`HTTP ${response.status}: ${response.statusText}`);
                    console.log(`⚠️ Webhook failed with status ${response.status}: ${webhookLog.webhookId}`);
                }

            } catch (error) {
                lastError = error;
                console.log(`❌ Webhook attempt ${attempt + 1} failed: ${error.message}`);

                await this.updateWebhookLog(webhookLog._id, {
                    retryCount: attempt,
                    errorMessage: error.message,
                    status: error.code === 'ECONNABORTED' ? 'timeout' : 'failed',
                    requestEndTime: new Date()
                });
            }

            // Wait before retry (exponential backoff)
            if (attempt < webhookLog.maxRetries) {
                const delay = Math.min(1000 * Math.pow(2, attempt), 10000); // Max 10 seconds
                console.log(`⏳ Waiting ${delay}ms before retry...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }

        // All retries failed
        await this.updateWebhookLog(webhookLog._id, {
            status: 'failed',
            errorMessage: lastError?.message || 'All retry attempts failed'
        });

        console.log(`💥 Webhook completely failed: ${webhookLog.webhookId}`);
        return {
            success: false,
            webhookId: webhookLog.webhookId,
            error: lastError?.message || 'All retry attempts failed',
            attempts: webhookLog.maxRetries + 1
        };
    }

    async updateWebhookLog(logId, updates) {
        try {
            await WebhookLog.findByIdAndUpdate(logId, {
                ...updates,
                updatedAt: new Date()
            });
        } catch (error) {
            console.error('❌ Error updating webhook log:', error.message);
        }
    }

    // Get webhook logs with filtering
    async getWebhookLogs(filters = {}, options = {}) {
        const {
            page = 1,
            limit = 50,
            sortBy = 'createdAt',
            sortOrder = 'desc'
        } = options;

        const skip = (page - 1) * limit;
        const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

        try {
            const logs = await WebhookLog.find(filters)
                .sort(sort)
                .skip(skip)
                .limit(limit)
                .lean();

            const total = await WebhookLog.countDocuments(filters);

            return {
                logs,
                pagination: {
                    page,
                    limit,
                    total,
                    pages: Math.ceil(total / limit)
                }
            };
        } catch (error) {
            console.error('❌ Error fetching webhook logs:', error.message);
            throw error;
        }
    }

    // Get webhook statistics
    async getWebhookStats(timeRange = '24h') {
        const now = new Date();
        let startDate;

        switch (timeRange) {
            case '1h':
                startDate = new Date(now.getTime() - 60 * 60 * 1000);
                break;
            case '24h':
                startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                break;
            case '7d':
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            default:
                startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        }

        try {
            const stats = await WebhookLog.aggregate([
                {
                    $match: {
                        createdAt: { $gte: startDate }
                    }
                },
                {
                    $group: {
                        _id: '$status',
                        count: { $sum: 1 },
                        avgResponseTime: { $avg: '$responseTime' }
                    }
                }
            ]);

            const total = await WebhookLog.countDocuments({
                createdAt: { $gte: startDate }
            });

            return {
                timeRange,
                total,
                stats: stats.reduce((acc, stat) => {
                    acc[stat._id] = {
                        count: stat.count,
                        avgResponseTime: Math.round(stat.avgResponseTime || 0)
                    };
                    return acc;
                }, {})
            };
        } catch (error) {
            console.error('❌ Error fetching webhook stats:', error.message);
            throw error;
        }
    }
}

// Express App Setup
const app = express();
app.use(express.json());

const webhookService = new WebhookService();


app.post('/api/cards', async (req, res) => {
    try {
        const { title, description, userId } = req.body;

        // Your existing card creation logic here
        const newCard = {
            id: crypto.randomUUID(),
            title,
            description,
            userId,
            createdAt: new Date()
        };

        console.log('🎴 Card created:', newCard);

        // Send webhook notification
        const webhookUrl = process.env.CARD_WEBHOOK

        const webhookPayload = {
            event: 'card.created',
            data: newCard,
            timestamp: new Date().toISOString()
        };

        // Send webhook asynchronously (don't wait for response)
        webhookService.sendWebhook({
            url: webhookUrl,
            payload: webhookPayload,
            triggerEvent: 'create_card',
            cardId: newCard.id,
            userId: userId
        }).catch(error => {
            console.error('Webhook sending failed:', error.message);
        });

        res.status(201).json({
            success: true,
            card: newCard,
            message: 'Card created successfully'
        });

    } catch (error) {
        console.error('❌ Error creating card:', error.message);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Webhook management endpoints
app.get('/api/webhooks/logs', async (req, res) => {
    try {
        const {
            status,
            triggerEvent,
            cardId,
            page = 1,
            limit = 50
        } = req.query;

        const filters = {};
        if (status) filters.status = status;
        if (triggerEvent) filters.triggerEvent = triggerEvent;
        if (cardId) filters.cardId = cardId;

        const result = await webhookService.getWebhookLogs(filters, {
            page: parseInt(page),
            limit: parseInt(limit)
        });

        res.json(result);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

app.get('/api/webhooks/stats', async (req, res) => {
    try {
        const { timeRange = '24h' } = req.query;
        const stats = await webhookService.getWebhookStats(timeRange);
        res.json(stats);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

app.get('/api/webhooks/logs/:webhookId', async (req, res) => {
    try {
        const { webhookId } = req.params;
        const log = await WebhookLog.findOne({ webhookId });

        if (!log) {
            return res.status(404).json({ error: 'Webhook log not found' });
        }

        res.json(log);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

// Manual webhook retry endpoint
app.post('/api/webhooks/retry/:webhookId', async (req, res) => {
    try {
        const { webhookId } = req.params;
        const log = await WebhookLog.findOne({ webhookId });

        if (!log) {
            return res.status(404).json({ error: 'Webhook log not found' });
        }

        const result = await webhookService.sendWebhook({
            url: log.webhookUrl,
            payload: log.requestPayload,
            triggerEvent: log.triggerEvent,
            cardId: log.cardId,
            userId: log.userId
        });

        res.json({
            message: 'Webhook retry initiated',
            result
        });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});