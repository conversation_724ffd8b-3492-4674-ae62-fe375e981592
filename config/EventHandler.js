const Task = require('../models/EventTask');
 
const User = require("../models/user")
const Country = require("../models/Countries");
const BinType = require("../models/BinType");
const CardProgrammeType = require("../models/CardProgrammeType");
const ProductVersion = require("../models/productVersions");
const ProductCurrency = require("../models/ProductCurrency");
const CardScheme = require("../models/CardScheme");
const ProgrammeManagerType = require("../models/ProgrammeManagerType");
const BinUsage = require("../models/BinUsage");
const BinCategory = require("../models/BinCategory");
const BinVariant = require("../models/BinVariant");
const ProgrammeType = require("../models/ProgrammeType");
const BinRange = require("../models/BinRange");



const getModelByType = (type) => {
    const modelMap = {
        'country': Country,
        'bin type': BinType,
        'bin range': BinRange,
        'card programme type': CardProgrammeType,
        'product version': ProductVersion,
        'programme type': ProgrammeType,
        'currency': ProductCurrency,
        'card scheme': CardScheme,
        'programme manager type': ProgrammeManagerType,
        'bin category': BinCategory,
        'bin variant': BinVariant,
        'bin usage': BinUsage
    };

    return modelMap[type.toLowerCase()];
};


const updateRelatedRecord = async (type, refId, status, reason = null, actionPerformedBy = null, incrementVersion = false) => {
    try {
        const Model = getModelByType(type);
        if (!Model) {
            return null;
        }

        const record = await Model.findById(refId);
        if (!record) {
            return null;
        }

        const updateData = {
            status,
            reason: reason,
            reasonUpdatedAt: reason ? new Date() : null,
            actionPerformedBy: actionPerformedBy
        };

        if (incrementVersion && status === 'active') {
            let currentVersion = parseFloat(record.version) || 0.0;
            let updatedVersion = (currentVersion + 0.1).toFixed(1);
            updateData.version = updatedVersion;
        }

        const updatedRecord = await Model.findByIdAndUpdate(refId, updateData, { new: true });
        return updatedRecord;
    } catch (error) {
        throw error;
    }
};


const sendEmailNotification = async (user, task, action, reason ) => {
    console.log(`Sending ${action} email to ${user.email} for task ${task._id} ${reason ? 'with reason: ' + reason : ''}`);
    
};

const createTask = async (taskData) => {
    try {
        const newTask = new Task({
            refId: taskData.refId,
            type: taskData.type,
            title: taskData.title,
            status: taskData.status || 'Pending',
            date: taskData.date || new Date(),
            user: taskData.user,
            ipAddress: taskData.ipAddress,
        });

        const user = await User.findById(taskData.user);
        if (!user) {
            throw new Error('User not found');
        }

        const savedTask = await newTask.save();

        // Set the related record status to pending when task is created
        await updateRelatedRecord(taskData.type, taskData.refId, 'pending', 'Task created and awaiting approval', taskData.user);

        await sendEmailNotification(user, savedTask, 'create');

        return savedTask;
    } catch (err) {
        throw err;
    }
};

const updateTaskStatus = async (taskId, newStatus, actionPerformedBy = null) => {
    try {
        if (!taskId || !newStatus) {
            throw new Error('Task ID and new status are required.');
        }

        const currentTask = await Task.findById(taskId);
        if (!currentTask) {
            throw new Error('Task not found.');
        }

        const type = currentTask.type;
        const refId = currentTask.refId;

        // Update related record to active status and increment version
        await updateRelatedRecord(
            type,
            refId,
            'active',
            'Request approved and activated',
            actionPerformedBy,
            true
        );

        const updatedTask = await Task.findByIdAndUpdate(
            taskId,
            {
                status: newStatus,
                actionPerformedBy: actionPerformedBy
            },
            { new: true, useFindAndModify: false }
        );

        // Send completion email
        const user = await User.findById(currentTask.user);
        if (user) {
            await sendEmailNotification(user, updatedTask, 'complete');
        }

        return updatedTask;
    } catch (err) {
        throw err;
    }
};

const declineTask = async (taskId, reason, actionPerformedBy = null) => {
    try {
        if (!taskId || !reason) {
            throw new Error('Task ID and decline reason are required.');
        }

        const currentTask = await Task.findById(taskId);
        if (!currentTask) {
            throw new Error('Task not found.');
        }

        const type = currentTask.type;
        const refId = currentTask.refId;

        // Update related record to declined status with reason
        await updateRelatedRecord(
            type,
            refId,
            'decline',
            reason,
            actionPerformedBy
        );

        // Update task with declined status and reason
        const updatedTask = await Task.findByIdAndUpdate(
            taskId,
            {
                status: 'Declined',
                declineReason: reason,
                declinedAt: new Date(),
                actionPerformedBy: actionPerformedBy
            },
            { new: true, useFindAndModify: false }
        );

        // Send decline email with reason
        const user = await User.findById(currentTask.user);
        if (user) {
            await sendEmailNotification(user, updatedTask, 'decline', reason);
        }

        return updatedTask;
    } catch (err) {
        throw err;
    }
};

const modifyTask = async (taskId, reason, actionPerformedBy = null) => {
    try {
        if (!taskId || !reason) {
            throw new Error('Task ID and modification reason are required.');
        }

        const currentTask = await Task.findById(taskId);
        if (!currentTask) {
            throw new Error('Task not found.');
        }

        const type = currentTask.type;
        const refId = currentTask.refId;

        // Update related record to modification required status with reason
        await updateRelatedRecord(
            type,
            refId,
            'modify',
            reason,
            actionPerformedBy
        );

        // Update task with modification status and reason
        const updatedTask = await Task.findByIdAndUpdate(
            taskId,
            {
                status: 'Modification Required',
                modificationReason: reason,
                modificationRequestedAt: new Date(),
                actionPerformedBy: actionPerformedBy
            },
            { new: true, useFindAndModify: false }
        );

        // Send modification email with reason
        const user = await User.findById(currentTask.user);
        if (user) {
            await sendEmailNotification(user, updatedTask, 'modify', reason);
        }

        return updatedTask;
    } catch (err) {
        throw err;
    }
};

// Function to get task with full details including reasons
const getTaskById = async (taskId) => {
    try {
        const task = await Task.findById(taskId).populate('user', 'name email');
        if (!task) {
            throw new Error('Task not found.');
        }
        return task;
    } catch (err) {
        throw err;
    }
};

// Function to get all tasks with optional filtering
const getAllTasks = async (filters = {}) => {
    try {
        const query = {};

        if (filters.status) {
            query.status = filters.status;
        }

        if (filters.type) {
            query.type = filters.type;
        }

        if (filters.user) {
            query.user = filters.user;
        }

        const tasks = await Task.find(query)
            .populate('user', 'name email')
            .populate('actionPerformedBy', 'name email')
            .sort({ createdAt: -1 });

        return tasks;
    } catch (err) {
        throw err;
    }
};

// Function to get records by type with status and reason
const getRecordsByType = async (type, filters = {}) => {
    try {
        const Model = getModelByType(type);
        if (!Model) {
            throw new Error(`No model found for type: ${type}`);
        }

        const query = {};

        if (filters.status) {
            query.status = filters.status;
        }

        const records = await Model.find(query)
            .populate('actionPerformedBy', 'name email')
            .sort({ updatedAt: -1 });

        return records;
    } catch (err) {
        throw err;
    }
};

const updateTaskByRecordId = async ( recordId, name ) => {
    try {
        if (  !recordId  ) {
            throw new Error('Type, record ID, and new status are required.');
        }

        // Find the task associated with the record
        const task = await Task.findOne({  refId: recordId });
        if (!task) {
            throw new Error('No task found for the given record.');
        }

        // Update the task status
        task.status = "Pending";
        task.title = task.title + ` - Updated to "` + name+`"`;
        task.updatedAt = new Date();


        const updatedTask = await task.save();

        return updatedTask;
    } catch (err) {
        throw err;
    }
};

module.exports = {
    createTask,
    updateTaskStatus,
    declineTask,
    modifyTask,
    getTaskById,
    getAllTasks,
    getRecordsByType,
    updateTaskByRecordId
};
