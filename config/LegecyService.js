require('dotenv').config();
const axios = require('axios');
const { generateJwtToken } = require('./jwtService');

/**
 * Re-exported generateJwtToken (keeps previous API)
 */
async function fetchBalance(accountIdentification) {
    if (!accountIdentification) {
        throw new Error('accountIdentification is required');
    }

    const token = generateJwtToken();
    const apiUrl = `${process.env.CARD_AUTH_URL}/api/itcard/balance-check`;

    const response = await axios.get(apiUrl, {
        headers: {
            Authorization: `Bearer ${token}`
        },
        params: { accountIdentification }
    });

    return response.data;
}

module.exports = {
    generateJwtToken,
    fetchBalance
};
