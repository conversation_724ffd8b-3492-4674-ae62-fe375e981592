import path from "path";

function createToken() {
    const certPath = path.join(__dirname, '..', 'config', 'RYVL_API.crt');

    const payload = {
        sub: 'subject', // Subject
        iss: 'issuer', // Issuer URL
        exp: Math.floor(Date.now() / 1000) + 60, // Expiration time (1 minute from now)
    };

    // Sign the token with ES512
    return jwt.sign(payload, privateKey, {algorithm: 'ES512'});
}
