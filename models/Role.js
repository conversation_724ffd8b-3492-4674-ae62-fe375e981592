const mongoose = require('mongoose')

const RoleSchema = new mongoose.Schema({
    name: { type: String, required: true },
    description: { type: String, default: '' },
    permissions: { type: [String], default: [] },
    dashboard: { type: String ,enum: ['infinity-dashboard', 'pm-dashboard', 'b2b-dashboard', 'cardholder-dashboard'], default: "infinity-dashboard"},
    deleted_at: {type: Date, default: null}
}, { timestamps: true })

module.exports = mongoose.model('Role', RoleSchema)
