const mongoose = require("mongoose");

const logSchema = new mongoose.Schema({
    remoteAddr: { type: String, required: true },
    userIdentity: { type: String, default: "Anonymous" },
    timestamp: { type: Date, default: Date.now },
    clientTime: { type: String },
    method: { type: String, required: true },
    url: { type: String, required: true },
    httpVersion: { type: String, required: true },
    status: { type: Number, required: true },
    responseSize: { type: String, required: true },
    referer: { type: String, default: "Direct Access" },
    userAgent: { type: String, required: true },
    responseTime: { type: String, required: true },
    requestBody: mongoose.Schema.Types.Mixed,
    responseBody: mongoose.Schema.Types.Mixed
});

// Export model


module.exports = mongoose.models.Log || mongoose.model('Log', logSchema)
