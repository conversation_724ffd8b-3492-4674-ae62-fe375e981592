const mongoose = require('mongoose');

const taskSchema = new mongoose.Schema({
    refId: {
        type: String,
        required: true
    },
    type: {
        type: String,
        required: true
    },
    title: {
        type: String,
        required: true
    },
    status: {
        type: String,
        enum: ['Pending', 'In Progress', 'Done', 'Declined', 'Modification Required', 'Canceled'],
        default: 'Pending'
    },
    date: {
        type: Date,
        default: Date.now
    },
    user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    ipAddress: {
        type: String,
        required: true
    },
    // New fields for decline functionality
    declineReason: {
        type: String,
        default: null
    },
    declinedAt: {
        type: Date,
        default: null
    },
    // New fields for modification functionality
    modificationReason: {
        type: String,
        default: null
    },
    modificationRequestedAt: {
        type: Date,
        default: null
    },
    // Track who performed the action
    actionPerformedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        default: null
    }
}, {
    timestamps: true
});

module.exports = mongoose.model('Task', taskSchema);
