const mongoose = require('mongoose');
const {Schema} = require("mongoose");

const CardImageSchema = new mongoose.Schema({
    front_side: {type: String, required: true},
    company: {type: Schema.Types.ObjectId, ref: 'Company'},
    back_side: {type: String, required: true},
    front_side_name: {type: String},
    back_side_name: {type: String},
    product_version: {type: Schema.Types.ObjectId, ref: 'ProductVersion', default: null},
}, {timestamps: true});

module.exports = mongoose.model('CardImage', CardImageSchema);
