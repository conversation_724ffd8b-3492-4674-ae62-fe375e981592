const mongoose = require('mongoose');
const {Schema} = require("mongoose");

/**
 * Bin Range Schema
 * Represents a bin range in the system
 */
const binRangeSchema = new Schema(
    {
        // Reference to the bin type
        binType: {
            type: Schema.Types.ObjectId,
            ref: "BinType",
            required: [true, "Bin type is required"],
        },

        // Bin code (full code)
        binCode: {
            type: String,
            required: [true, "Bin code is required"],
            trim: true,
        },

        // Bin code prefix (first part of the code)
        binCodePrefix: {
            type: String,
            required: [true, "Bin code prefix is required"],
            trim: true,
        },

        // Bin code suffix (last part of the code)
        binCodeSuffix: {
            type: String,
            required: [true, "Bin code suffix is required"],
            trim: true,
        },

        // Reference to the currency
        currency: {
            type: Schema.Types.ObjectId,
            ref: "ProductCurrency",
            required: [true, "Currency is required"],
        },

        // Bin range start
        bin_start: {
            type: String,
            required: [true, "Bin range start is required"],
            trim: true,
        },

        // Bin range end
        bin_end: {
            type: String,
            required: [true, "Bin range end is required"],
            trim: true,
        },


        status: {
            type: String,
            enum: ['active', 'inactive', 'pending','modify',"decline"], // Define possible values for status
            default: 'pending' // Set a default status if needed
        },

        // User who created this bin range
        created_by: {
            type: Schema.Types.ObjectId,
            ref: "User",
            required: true,
        },

        // Creation timestamp
        created_at: {
            type: Date,
            default: Date.now,
        },

        // Last update timestamp
        updated_at: {
            type: Date,
            default: Date.now,
        },

        // User who last updated this bin range
        updated_by: {
            type: Schema.Types.ObjectId,
            ref: "User",
        },

        // Reason for modification or decline (if applicable)
        reason: {
            type: String,
            trim: true,
        },
        version: {
            type: String, default: null
        },
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at",
        },
    },
)
//
// // Create indexes for better query performance





//
// // Virtual for getting the full bin range as a formatted string



//
// // Pre-save middleware to ensure bin_start is less than bin_end

//     // Remove spaces and convert to numbers for comparison


//



//
//     next()

//
// // Static method to find overlapping bin ranges



//

//         $or: [
//             // Case 1: New range completely contains an existing range


//                     $and: [
//                         { $lte: [startNum, { $toInt: { $replaceAll: { input: "$bin_start", find: " ", replacement: "" } } }] },
//                         { $gte: [endNum, { $toInt: { $replaceAll: { input: "$bin_end", find: " ", replacement: "" } } }] },



//             // Case 2: New range is completely contained within an existing range


//                     $and: [
//                         { $gte: [startNum, { $toInt: { $replaceAll: { input: "$bin_start", find: " ", replacement: "" } } }] },
//                         { $lte: [endNum, { $toInt: { $replaceAll: { input: "$bin_end", find: " ", replacement: "" } } }] },



//             // Case 3: New range overlaps with the start of an existing range


//                     $and: [
//                         { $lte: [startNum, { $toInt: { $replaceAll: { input: "$bin_end", find: " ", replacement: "" } } }] },
//                         { $gte: [endNum, { $toInt: { $replaceAll: { input: "$bin_end", find: " ", replacement: "" } } }] },



//             // Case 4: New range overlaps with the end of an existing range


//                     $and: [
//                         { $lte: [startNum, { $toInt: { $replaceAll: { input: "$bin_start", find: " ", replacement: "" } } }] },
//                         { $gte: [endNum, { $toInt: { $replaceAll: { input: "$bin_start", find: " ", replacement: "" } } }] },




//         status: "active",

//
//     // Exclude the current document if updating



//



// Create and export the model
const BinRange = mongoose.model("BinRange", binRangeSchema)

module.exports = BinRange

