const mongoose = require("mongoose");
const PermissionAuditSchema = new mongoose.Schema({
    key: { type: String, required: true },
    enabled: { type: Boolean, required: true }
}, { _id: false });

const PermissionLogsSchema = new mongoose.Schema({
    action: { type: String, required: true },  // e.g., "Permission Update", "User Created"
    details: { type: String },                 // extra info about the change
    username: { type: String },                // person's full name
    email: { type: String },                   // person's email
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }, // store ObjectId
}, { timestamps: true });

const companySchema = new mongoose.Schema({
    company_name: String,
    company_industry: String,
    company_number: String,
    company_phone: String,
    registration_date: Date,
    contact_name: String,
    contact_role: String,
    company_email: String,
    country_of_incorporation: String,
    company_website: String,
    type_of_business: String,
    card_usage: String,
    cardholder_groups: String,
    fund_loading: Number,
    business_sector: String,
    regions: String,
    countries: String,
    business_purpose: String,
    card_user_groups: String,
    number_of_cards: Number,
    monthly_loading_value: Number,
    admin_first_name: String,
    admin_last_name: String,
    admin_role: String,
    admin_email: String,
    admin_phone: String,

    registered_address: {
        street: String,
        building_number: String,
        apartment_number: String,
        city: String,
        state: String,
        postal_code: String,
        country: String,
    },
    operational_address: {
        street: String,
        building_number: String,
        apartment_number: String,
        city: String,
        state: String,
        postal_code: String,
        country: String,
    },
    permissions: { type: [String], default: [] },
    permissionAudit: { type: [PermissionAuditSchema], default: [] },
    permissionsLog: { type: [PermissionLogsSchema], default: [] },

    dashboardStatus: String,
    status: {type:String, default:"active"},
    ryvyl_id: String,
    critical_bins: { type: String, default: "1000"},
    created_at: { type: Date, default: Date.now },
    deleted_at: { type: Date, default: null },
}, { timestamps: true });

module.exports = mongoose.model('Company', companySchema);
