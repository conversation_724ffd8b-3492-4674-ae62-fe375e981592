const mongoose = require("mongoose")

// Define the ZoneMethodConfig schema
const ZoneMethodConfigSchema = new mongoose.Schema(
    {
        zoneId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "Zone",
            required: [true, "Zone ID is required"],
        },
        methodId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "DeliveryMethod",
            required: [true, "Method ID is required"],
        },
        standardWeight: {
            type: Number,
            required: [true, "Standard weight is required"],
            min: 0,
        },
        bulkWeight: {
            type: Number,
            required: [true, "Bulk weight is required"],
            min: 0,
        },
        deliveryTime: {
            type: String,
            required: [true, "Delivery time is required"],
            trim: true,
        },
        price: {
            type: Number,
            required: [true, "Price is required"],
            min: 0,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
)

// Create a compound index to ensure uniqueness of zone-method combinations
ZoneMethodConfigSchema.index({ zoneId: 1, methodId: 1 }, { unique: true })

// Create and export the ZoneMethodConfig model

module.exports = mongoose.models.ZoneMethodConfig || mongoose.model("ZoneMethodConfig", ZoneMethodConfigSchema)
