const mongoose = require("mongoose")

// Define the ZoneMethodConfig schema
const ZoneMethodConfigSchema = new mongoose.Schema(
    {
        zoneId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "Zone",
            required: [true, "Zone ID is required"],
        },
        country: {
            type: String,
            required: [true, "Country is required"],
            trim: true,
        },
        methodId: {
            type: String,
            required: [true, "Method ID is required"],
        },
        standardWeight: {
            type: Number,
            required: [true, "Standard weight is required"],
            min: 0,
        },

        deliveryTime: {
            type: String,
            required: [true, "Delivery time is required"],
            trim: true,
        },
        price: {
            type: Number,
            required: [true, "Price is required"],
            min: 0,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
)

// Create a compound index to ensure uniqueness of zone-country-method combinations


// Create and export the ZoneMethodConfig model
module.exports = mongoose.model("ZoneMethodConfig", ZoneMethodConfigSchema)
