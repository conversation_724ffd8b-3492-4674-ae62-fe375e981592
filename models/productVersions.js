const mongoose = require("mongoose")
const {Schema} = require("mongoose");

// Define schema for ProductVersion
const productVersionSchema = new mongoose.Schema({
    version_name: {
        type: String,
        required: true,
        trim: true
    },

    version_code: {
        type: String,
        trim: true
    },
    product_reference: {
        type: String,
        trim: true
    },

    status: {
        type: String,
        enum: ['active', 'inactive', 'pending', 'modify', "decline"], // Define possible values for status
        default: 'pending' // Set a default status if needed
    },
    version: {
        type: String, default: null
    }, reason: {
        type: String, default: null
    },
    company: {type: Schema.Types.ObjectId, ref: 'Company', required: false}, // Add created_by field
    created_by: {type: Schema.Types.ObjectId, ref: 'User', required: true}, // Add created_by field
    created_at: {type: Date, default: Date.now}, // Set created_at with default value
    deleted_at: {type: Date, default: null} // Set deleted_at, default null
})

// Create model
const ProductVersion = mongoose.model("ProductVersion", productVersionSchema)

module.exports = ProductVersion
