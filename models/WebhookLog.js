// models/WebhookLog.js
const mongoose = require('mongoose');

const webhookLogSchema = new mongoose.Schema({
    event: String, // e.g., "createCard"
    webhookUrl: String,
    payloadSent: mongoose.Schema.Types.Mixed,
    responseStatus: Number,
    responseBody: mongoose.Schema.Types.Mixed,
    error: mongoose.Schema.Types.Mixed,
    createdAt: {
        type: Date,
        default: Date.now
    }
});

module.exports = mongoose.model('WebhookLog', webhookLogSchema);
