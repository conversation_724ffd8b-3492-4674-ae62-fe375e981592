const mongoose = require('mongoose');
const {Schema} = require("mongoose");

const CardSchema = new mongoose.Schema({
    cardHash: {
        type: String,
        required: true,
    },
    cardKey: {
        type: String,
        required: true,
        unique: true,
    },
    expDate: {
        type: String,
        required: true,
        validate: {
            validator: function (value) {
                // Check format MM/YYYY
                return /^\d{2}\/\d{4}$/.test(value);
            },
            message: 'Invalid expiration date format. Use MM/YYYY.',
        },
    },
    status: {
        type: String,
        required: true,
        enum: ['ACTIVE', 'INACTIVE', 'BLOCKED','ORDERED'], // Example valid statuses
    },
    statusCode: {
        type: String,
        required: true,
    },
    kind: {
        type: String,
        required: true,
        enum: ['DEBIT', 'CREDIT', 'PREPAID'], // Define valid card types
    },
    productCode: {
        type: String,
        required: true,
    },
    productDesc: {
        type: String,
    },
    main: {
        type: Boolean,
        required: true,
    },
    holder: {
        type: String,
        required: true,
    },
    accNo: {
        type: String,
        required: true,
    },
    embossName1: {
        type: String,
        required: true,
    },


    nickName: {
        type: String
    },
    set_pin: {type: Boolean, default: false},
    set_password: {type: Boolean, default: false},
    current_password: { type: String },
    authPhoneNumber: { type: String },
    deliveryMethod: { type:  mongoose.Schema.Types.Mixed },
    cardMask: {
        type: String,
        required: true,
        validate: {
            validator: function (value) {
                // Check for valid masked format
                return /^\d{6}\*{6}\d{4}$/.test(value);
            },
            message: 'Invalid card mask format.',
        },
    },
    parent:  {type: Schema.Types.ObjectId, ref: 'B2BDebitAccount', required: true},
    cardholder:  {type: Schema.Types.ObjectId, ref: 'IndividualOnboarding', required: true},
}, {
    timestamps: true, // Adds `createdAt` and `updatedAt` timestamps
});









const Card = mongoose.model('B2BCard', CardSchema);

module.exports = Card;
