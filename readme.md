
# Ryvyl Backend

A production-ready Node.js + Express API backend (MongoDB). This README covers project structure, local development, environment variables, testing, and detailed deployment instructions including Plesk.

---

## Table of contents
- Project overview
- Requirements
- Quick start (local)
- Environment variables
- Scripts
- Project structure
- API overview
- File uploads & storage
- Logging & error handling
- Security & CORS
- Troubleshooting
- Deployment
  - Deploy to Plesk (step-by-step)
  - Optional: PM2 / standalone server
- Contributing

---

## Project overview
This repository implements the backend API for Ryvyl. Primary technologies:
- Node.js, Express
- MongoDB (mongoose)
- JWT-based authentication
- File uploads (multer or similar)
- Logging with morgan and a Mongoose Log model
- Centralized API error handling

Main entry: `index.js`. Routes live in `routes/`, controllers in `controllers/`, middleware in `middleware/`, and models in `models/`.

---

## Requirements
- Node.js 16+ (use the version specified in `.nvmrc` or package.json engines if present)
- npm (or yarn)
- MongoDB instance (Atlas, self-hosted, or managed)
- OpenSSL (optional — for generating local certificates)

---

## Quick start (local)

1. Install dependencies
```powershell
npm install
```

2. Copy example env file and edit
```powershell
copy example.env .env
# or
cp example.env .env
```
Edit `.env` with values for DATABASE_URL, JWT_SECRET, PORT, etc.

3. Run the app
```powershell
npm start
# or for development with nodemon (if configured)
npm run dev
```

4. Test a route (example)
```powershell
curl http://localhost:3000/api/health
```

---

## Environment variables
Keep all secrets in `.env`. Typical variables:

- DATABASE_URL — MongoDB connection string (mongodb+srv://... or mongodb://...)
- PORT — app port (Plesk sets this automatically; default 3000)
- NODE_ENV — production | development
- JWT_SECRET — secret for JWT tokens
- JWT_EXPIRES_IN — token expiration
- UPLOAD_DIR — path to uploads folder (if configurable)
- Any other API keys or config used by controllers

Do not commit `.env` or private keys to source control.

---

## Scripts
Open `package.json` to see scripts. Common commands:
- npm start — start server
- npm run dev — start in development (with nodemon)
- npm test — run test suite (if present)
- npm run lint — lint code

---

## Project structure (typical)
- index.js — application entry
- routes/ — route definitions
- controllers/ — request handlers
- middleware/ — auth, file-upload, error handling
- models/ — mongoose models (e.g., Log)
- config/ — API error helpers and config
- uploads/ — uploaded files (must be writable by server)
- example.env — example environment variables
- vercel.json, private.pem, public.pem — deployment/config artifacts

---

## API overview
Routes are mounted in `index.js`. Notable groups:
- /api/auth — authentication endpoints
- /api/user — user management
- /api/card* — card related APIs (programs, type, bin)
- /api/client — client management
- /api/onboarding/personal — onboarding and ID uploads
- /api/images — image endpoints
- /api/events, /api/webhook, integration routes — integrations and legacy endpoints

Open the `routes/` folder for detailed endpoints and request/response shapes.

---

## File uploads & storage
- Upload middleware lives in `middleware/file-upload.js`.
- Uploaded files are stored in `uploads/` (ensure the directory exists and is writable).
- Add `uploads/` to `.gitignore`.
- For production, consider using S3 or other object storage and keep only temporary files locally.

---

## Logging & error handling
- Request logging: morgan (configured in `index.js`).
- Long-term logs can be stored in MongoDB via `models/Log.js`.
- Central API errors handled by `config/ApiError.js` (check `index.js` to see how it's wired).
- Response bodies may be captured for auditing — verify GDPR/privacy requirements before logging sensitive data.

---

## Security & CORS
- Helmet is used to set secure headers.
- CORS allowlist configured in `index.js` — update allowed origins for production.
- Keep secrets out of VCS. Use strong JWT secrets and rotate them periodically.
- If using mTLS / client certificates, store CA keys securely and do not commit them.

---

## Troubleshooting (local)
- Check node process:
```powershell
Get-Process -Name node
```
- Check logs (stdout/stderr) where you run the app.
- Common issues: wrong MongoDB URI, missing env vars, directory permission for uploads.

---

## Deployment

General notes:
- The app expects `process.env.PORT` for the server port.
- Configure environment variables on the host; do not rely on a committed `.env`.
- Ensure the `uploads/` directory is persistent and writable.

### Deploy to Plesk (step-by-step)

Plesk offers a Node.js extension that simplifies deployment. These steps assume you have a Plesk subscription and access to the domain/hosting area.

1. Prepare the project
- Ensure `package.json` has a meaningful `start` script (e.g., `node index.js` or `node ./index.js`).
- Commit your code or prepare a zip archive.

2. Upload or connect repository
- Option A: Use Plesk's Git > Create Repository and pull from your remote.
- Option B: Upload source archive via File Manager and extract to the desired domain/subscription directory.
- Document root should point to your project directory (not `/public` unless your app serves static files there).

3. Enable Node.js support for the domain
- In Plesk, go to Domains > your domain > Node.js.
- Click "Enable Node.js".

4. Configure application settings
- Application root: path to your project folder (where package.json exists).
- Application startup file: `index.js` (or your specific entry file).
- Application mode: `production` (or `development` while testing).
- Node.js version: select a compatible version (>=16 recommended).
- Environment variables: add each .env key/value in the Node.js settings form (DATABASE_URL, JWT_SECRET, PORT is typically set for you by Plesk but you can set it if needed).
  - Example:
    - KEY: DATABASE_URL
    - VALUE: **************************************************

5. Install dependencies
- Use the "NPM Install" button in the Plesk Node.js panel or access the server via SSH and run:
```bash
cd /var/www/vhosts/example.com/httpdocs/your-app
npm ci --production
```
(use `npm install` if you need dev dependencies or `npm ci` for reproducible install)

6. Ensure writable uploads directory
- Ensure `uploads/` (or configured UPLOAD_DIR) exists and is writable by the user that Plesk runs Node apps as:
```bash
mkdir -p uploads
chown -R <plesk-user>:<plesk-group> uploads
chmod -R 755 uploads
```
(Adjust ownership according to your hosting setup; Plesk may manage this automatically)

7. Start the application
- Use the "Enable Node.js" > "NPM start" or "Restart" controls in Plesk.
- Plesk will run the defined `start` script. It sets an internal `PORT` env var and reverse-proxies HTTP(S) traffic to your Node process.

8. SSL / HTTPS
- Use Plesk's Let's Encrypt extension to generate and install an SSL certificate for your domain.
- Plesk handles TLS termination; your Node app will see proxied traffic. Ensure secure cookies and trust proxy in Express:
```js
app.set('trust proxy', 1);
```
- If you require client certificates (mTLS), you must set up a custom TLS termination that validates client certs or run the Node app on a dedicated port behind a properly configured NGINX that enforces mTLS (advanced).

9. Logs & monitoring
- View application logs in Plesk Node.js panel (stdout/stderr) or use the Plesk File Manager to inspect log files.
- Consider integrating external monitoring (PM2 monitoring, LogDNA, Papertrail).

10. Environment specific tweaks
- If your app relies on `process.env.PORT` be sure not to hardcode a port.
- For heavy workloads, consider using a separate process manager (PM2) and run it under a system service, but note that Plesk Node.js integration manages the process itself.

Common Plesk troubleshooting:
- 502 Bad Gateway: check that the Node process is running and bound to the expected port; verify startup file and node version.
- Dependencies missing: ensure `npm install` was executed and node_modules exists.
- File permission errors: fix ownership and permissions for upload/static directories.

---

### Optional: Running with PM2 (if not using Plesk)
If you need a process manager outside Plesk:
```bash
npm install -g pm2
pm2 start index.js --name ryvyl-backend --env production
pm2 save
pm2