const IndividualOnboarding = require("../models/IndividualOnboarding")
const Account = require("../models/Account")
const AccountCard = require("../models/AccountCard")
const { sendPostRequest } = require("../config/ApiInstense")
const mongoose = require("mongoose")
const { registerUser } = require("./userController")
const User = require("../models/user")
const VALIDATION_RULES = require("../utils/validation-constants")
const { validateOnboardingPersonal } = require("../validations/onboardingPersonalValidation")

 
const {
    isValidObjectId
} = require("../utils/validation-utils")
const { updateIdDocumentImages } = require("../config/id-document-service")
const { countries } = require('../config/currencies')
const { fetchBalance } = require("../config/LegecyService")
const B2BIndividualAccounts = require("../models/B2BIndividualAccounts")
const B2BAccount = require("../models/B2BAccount")

const createIndividualOnboarding = async (req, res) => {
    try {
        let {
            company,
            productVersions,
            personalInfo,
            address,
            idDocument,
            taxInfo,
            currency,
            applicationId,
            riskLevel,
            riskStatus,
            applicationStatus,
            applicationDate,
            mothersMaidenName,
            legalId,
            citizenship,
            birthCountry,
            clientCode,
            b2bClient,
        } = req.body;

        // Centralized validation
        const validationErrors = validateOnboardingPersonal({
            clientCode,
            personalInfo,
            mothersMaidenName,
            legalId,
            citizenship,
            address,
        });
        if (validationErrors.length > 0) {
            return res.status(400).json({ success: false, errors: validationErrors });
        }

        // Set default values if company is null
        let origin
        if (company == null) {
            company = "6785126247b8a6a67fbf7cad"
            productVersions = [new mongoose.Types.ObjectId("67caebe302cc703097f4b7df")]
            origin = "API"
        } else {
            // Validate company ID format if provided
            if (!isValidObjectId(company)) {
                return res.status(400).json({
                    message: "Invalid company ID format",
                })
            }
            origin = "Onboarding Form"
        }

        // Validate product versions if provided
        if (productVersions && Array.isArray(productVersions)) {
            for (const version of productVersions) {
                if (!isValidObjectId(version.toString())) {
                    return res.status(400).json({
                        message: "Invalid product version ID format",
                    })
                }
            }
        }

        // Check for existing client with the same clientCode
        const existingOnboarding = await IndividualOnboarding.findOne({ clientID: clientCode })
        if (existingOnboarding) {
            return res.status(400).json({
                message: "Client already exists with this client code",
            })
        }

        // Check for existing user with the same email
        const existingUser = await User.findOne({ email: personalInfo.email })
        if (existingUser) {
            return res.status(400).json({
                message: "Please use a unique email address!",
            })
        }

        // Handle B2B users - only save data, no API operations
        if (personalInfo.userType === "b2b") {
            try {
                const savedOnboarding = await registerB2bCardholder({
                    company,
                    productVersions,
                    personalInfo,
                    address,
                    idDocument,
                    taxInfo,
                    currency,
                    applicationId,
                    riskLevel,
                    riskStatus,
                    applicationStatus,
                    applicationDate,
                    mothersMaidenName,
                    legalId,
                    citizenship,
                    birthCountry,
                    clientCode,
                    origin,
                    b2bClient,
                })

                return res.status(201).json({
                    message: "B2B Client created successfully",
                    data: savedOnboarding,
                })
            } catch (error) {
                return res.status(400).json({
                    message: "Failed to create B2B client",
                    error: error.message,
                })
            }
        } else {
            // Handle regular users - perform API operations
            return await handleRegularUserOnboarding(
                {
                    clientCode,
                    personalInfo,
                    address,
                    productVersions,
                    company,
                    mothersMaidenName,
                    birthCountry,
                    legalId,
                    origin,
                    citizenship,
                    idDocument,
                    taxInfo,
                    currency,
                    applicationId,
                    riskLevel,
                    riskStatus,
                    applicationStatus,
                    applicationDate,
                },
                res,
            )
        }
    } catch (error) {
        if (error instanceof Error) {
            res.status(400).json({
                message: "Failed to submit onboarding application",
                error: error.message,
            })
        } else {
            res.status(500).json({
                message: "Internal server error",
                error: "An unexpected error occurred",
            })
        }
    }
}


// Extracted regular user onboarding logic
const handleRegularUserOnboarding = async (data, res) => {
    const {
        clientCode,
        personalInfo,
        address,
        productVersions,
        company,
        mothersMaidenName, 
        legalId,
        origin,
        citizenship,
        idDocument,
        taxInfo,
        currency,
        applicationId,
        riskLevel,
        riskStatus,
        applicationStatus,
        applicationDate,
    } = data

    // Prepare client data for API request
    const clientData = {
        clientCode: clientCode,
        firstName: personalInfo.firstName,
        lastName: personalInfo.lastName,
        phoneNumber: personalInfo.phoneNumber,
        address: {
            street: address.street,
            buildingNumber: address.buildingNumber,
            apartmentNumber: address.apartmentNumber,
            city: address.city,
            country: getCountryNumber(address.country),
            zipCode: address.zipCode,
        },
    }

    const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/clients"

    try {
        const result = await sendPostRequest(url, clientData)

        if (result.status === "APPROVED") {
            // Create new onboarding application
            const newOnboarding = new IndividualOnboarding({
                productVersion: productVersions,
                company: company,
                personalInfo: {
                    firstName: personalInfo.firstName,
                    middleName: personalInfo.secondName,
                    lastName: personalInfo.lastName,
                    dateOfBirth: new Date(personalInfo.birthDate),
                    email: personalInfo.email,
                    phone: personalInfo.phoneNumber,
                    mothersMaidenName,
                    authPhoneNumber: personalInfo.authPhoneNumber,
                    birthCountry: getCountryNumber(personalInfo.birthCountry),
                },
                legalId,
                origin: origin,
                citizenship: getCountryNumber(citizenship),
                address: {
                    ...address,
                    country: getCountryNumber(address.country),
                },
                idDocument: {
                    number: idDocument.number,
                    customerIdType: idDocument.customerIdType,
                    issueDate: idDocument.issueDate,
                    expiryDate: idDocument.expiryDate,
                    issuingCountry: getCountryNumber(idDocument.issuingCountry),
                    idAuthority: idDocument.idAuthority,
                },
                taxInfo,
                client: true,
                dashboardStatus: "ACTIVE",
                clientID: clientCode,
                cardCurrency: currency,
                applicationId: applicationId || null,
                riskLevel: riskLevel || null,
                riskStatus: riskStatus || null,
                applicationStatus: applicationStatus || null,
                applicationDate: applicationDate ? new Date(applicationDate) : null,
            })

            // Save to database
            const savedOnboarding = await newOnboarding.save()

            // Register user if origin is API
            if (origin.toUpperCase() === "API") {
                await registerUser(
                    personalInfo.firstName + " " + personalInfo.lastName,
                    personalInfo.email,
                    [],
                    "active",
                    "cardholder",
                    savedOnboarding._id,
                )
            }

            res.status(201).json({
                message: "Client created successfully",
                data: savedOnboarding,
            })
        } else {
            // Handle non-approved status from external API
            res.status(400).json({
                message: "Client creation failed at payment service",
                status: result.status,
            })
        }
    } catch (error) {
        res.status(400).json({
            message: "Failed to submit onboarding application",
            error: error.message,
        })
    }
}


const registerB2bCardholder = async (data) => {
    const {
        company,
        productVersions,
        personalInfo,
        address,
        idDocument,
        taxInfo,
        currency,
        applicationId,
        riskLevel,
        riskStatus,
        applicationStatus,
        applicationDate,
        mothersMaidenName,
        legalId,
        citizenship,
        origin,
        b2bClient,
    } = data

    if (!b2bClient) {
        throw new Error("B2B client ID is required")
    }

    const b2b = await B2BAccount.findById(b2bClient)
    if (!b2b) {
        throw new Error("B2B account not found")
    }

    const debitPayload = {
        currencyCode: "EUR",
        accNo: "**********************",
        owners: [
            {
                clientCode: b2b.clientCode,
                relationship: "OWN",
            },
        ],
    }

    const debitUrl = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/accounts/debitAccount"
    const personResult = await sendPostRequest(debitUrl, debitPayload)

    if (!personResult.accNo) {
        throw new Error("Failed to create debit account")
    }

    // Create new onboarding application
    const newOnboarding = new IndividualOnboarding({
        productVersion: productVersions,
        company: company,
        personalInfo: {
            firstName: personalInfo.firstName,
            middleName: personalInfo.secondName,
            lastName: personalInfo.lastName,
            dateOfBirth: new Date(personalInfo.birthDate),
            email: personalInfo.email,
            phone: personalInfo.phoneNumber,
            mothersMaidenName,
            authPhoneNumber: personalInfo.authPhoneNumber,
            birthCountry: getCountryNumber(personalInfo.birthCountry),
        },
        legalId,
        origin: origin,
        citizenship: getCountryNumber(citizenship),
        address: {
            ...address,
            country: getCountryNumber(address.country),
        },
        idDocument: {
            number: idDocument.number,
            customerIdType: idDocument.customerIdType,
            issueDate: idDocument.issueDate,
            expiryDate: idDocument.expiryDate,
            issuingCountry: getCountryNumber(idDocument.issuingCountry),
            idAuthority: idDocument.idAuthority,
        },
        taxInfo,
        client: true,
        dashboardStatus: "ACTIVE",
        clientID: "RYVL-"+generateRandomNumbers() ,
        cardCurrency: currency,
        applicationId: applicationId || null,
        riskLevel: riskLevel || null,
        riskStatus: riskStatus || null,
        applicationStatus: applicationStatus || null,
        applicationDate: applicationDate ? new Date(applicationDate) : null,
        userType: "b2b",
        b2bClient: b2bClient,
    })

    const savedOnboarding = await newOnboarding.save()

    // Create B2B individual account
    const b2bIndivAccount = new B2BIndividualAccounts({
        parent: savedOnboarding._id,
        account: personResult.accNo,
        status: personResult.status,
        currencyCode: personResult.currencyCode,
        currencyName: personResult.currencyName,
        owners: (personResult.owners || []).map((owner) => ({
            clientCode: owner.clientCode,
            relationship: owner.relationship,
            mainOwner: owner.mainOwner,
        })),
    })

    await b2bIndivAccount.save()

    return savedOnboarding
}

/**
 * Upload ID document images for a client
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const uploadIdDocumentImages = async (req, res) => {
    try {
        const { clientCode } = req.params

        if (!clientCode) {
            return res.status(400).json({
                message: "Client code is required",
            })
        }

        // Check if files are provided in the request
        let idFront, idBack

        // Handle different upload methods
        if (req.files) {
            // For multer or express-fileupload
            idFront = req.files.idFront || req.files["idFront"]
            idBack = req.files.idBack || req.files["idBack"]
        } else if (req.body) {
            // For base64 encoded images in request body
            idFront = req.body.idFront
            idBack = req.body.idBack
        }

        if (!idFront) {
            return res.status(400).json({
                message: "Front ID image is required",
            })
        }

        // Update ID document images
        const updatedOnboarding = await updateIdDocumentImages(clientCode, idFront, idBack)

        res.status(200).json({
            message: "ID document images uploaded successfully",
            data: {
                clientCode,
                idDocument: {
                    frontImagePath: updatedOnboarding.idDocument.frontImagePath,
                    backImagePath: updatedOnboarding.idDocument.backImagePath,
                },
            },
        })
    } catch (error) {
        res.status(error.message.includes("not found") ? 404 : 400).json({
            message: "Failed to upload ID document images",
            error: error.message,
        })
    }
}

// Get all individual onboarding applications
const getAllIndividualOnboardings = async (req, res) => {
    try {
        const onboardings = await IndividualOnboarding.find().populate("company","company_name").populate("cardCurrency").sort({ createdAt: -1 })

        res.status(200).json({
            message: "Onboarding applications retrieved successfully",
            data: onboardings,
        })
    } catch (error) {
        res.status(500).json({
            message: "Failed to retrieve onboarding applications",
            error: error instanceof Error ? error.message : "Unknown error",
        })
    }
}

// Get a specific individual onboarding application by ID
const getIndividualOnboardingById = async (req, res) => {
    try {
        if (!req.params.id) {
            return res.status(400).json({
                message: "Onboarding ID is required",
            })
        }

        const onboarding = await IndividualOnboarding.findById(req.params.id).populate("cardCurrency").populate("company")

        if (!onboarding) {
            return res.status(404).json({
                message: "Onboarding application not found",
            })
        }

        const account = await Account.find({ onboarding: req.params.id })
        const onboardingId = new mongoose.Types.ObjectId(req.params.id)

        const cards = await AccountCard.find({ onboarding: onboardingId }).sort({ createdAt: -1 })

        let balance = 0

        // Only try to fetch balance if there's at least one account
        if (account.length > 0) {
            try {
                const result = await fetchBalance(account[0].accountNumber)
                balance = result?.availableBalance ?? 0
            } catch {
                balance = 0
            }
        }

        res.status(200).json({
            message: "Onboarding application retrieved successfully",
            data: onboarding,
            account,
            cards,
            balance,
        })
    } catch (error) {
        res.status(500).json({
            message: "Failed to retrieve onboarding application",
            error: error instanceof Error ? error.message : "Unknown error",
        })
    }
}

const activateAccount = async (req, res) => {
     try {
        const { recordId } = req.body;

        if (!recordId) {
            return res.status(400).json({ error: 'recordId is required' });
        }

        const updatedRecord = await IndividualOnboarding.findByIdAndUpdate(
            recordId,
            { dashboardStatus: 'ACTIVE' },
            { new: true } // Returns the updated document
        );

        if (!updatedRecord) {
            return res.status(404).json({ error: 'Record not found' });
        }

        res.status(200).json({ message: 'Record activated successfully', data: updatedRecord });
    } catch (error) {
        res.status(500).json({ error: 'An error occurred while activating the record', details: error.message });
    }
}

// Helper functions

function generateRandomNumbers() {
    // 1: Create a `Set` object
    const uniqueNumbers = new Set()
    while (uniqueNumbers.size < 5) {
        // 2: Generate each random number
        uniqueNumbers.add(Math.floor(Math.random() * (10 - 5 + 1)) + 5)
    }
    // 3: Immediately insert them numbers into the Set...
    return Number(Array.from(uniqueNumbers).join(""))
}

function getCountryNumber(code) {
    if (!code) return null
    return countries.find((c) => c.code.toUpperCase() === code.toUpperCase())?.isoNumeric
}



// GET route to retrieve all onboarding applications

const listOfAccounts = async (req, res) => {
    try {
        const accounts = await Account.find({});
        res.status(200).json({
            message: "Accounts retrieved successfully",
            data: accounts,
        });
    } catch (error) {
        res.status(500).json({
            message: "Failed to retrieve accounts",
            error: error instanceof Error ? error.message : "Unknown error",
        });
    }
}

module.exports = {
    getAllIndividualOnboardings,
    getIndividualOnboardingById,
    createIndividualOnboarding,
    listOfAccounts,
    uploadIdDocumentImages,
    activateAccount
}
