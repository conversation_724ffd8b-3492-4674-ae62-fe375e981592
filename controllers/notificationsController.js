const postmark = require("postmark");
const Company = require("../models/company");

const POSTMARK_API_KEY = process.env.POSTMARK_API_KEY;
const POSTMARK_FROM = process.env.POSTMARK_FROM_EMAIL || "<EMAIL>";
const LOW_BIN_TEMPLATE = process.env.POSTMARK_TEMPLATE_ALIAS_LOW_BIN || "low-bin-alert";

const client = new postmark.ServerClient(POSTMARK_API_KEY);

/**
 * POST /critical-stock-email
 * Body: { companyId, criticalProducts: [...], threshold, timestamp }
 */
async function criticalStockEmail(req, res) {
  try {
    const { companyId, criticalProducts, threshold, timestamp } = req.body;
    if (!companyId || !Array.isArray(criticalProducts) || criticalProducts.length === 0) {
      return res.status(400).json({ success: false, message: "companyId and criticalProducts are required" });
    }

    const company = await Company.findById(companyId).lean();
    if (!company || !company.company_email) {
      return res.status(404).json({ success: false, message: "Company or company email not found" });
    }

    const to = company.company_email;
    const sendPromises = criticalProducts.map(async (product) => {
      const {
        productName,
        versionCode,
        cardRange,
        remaining,
        utilizationRate,
        threshold: productThreshold,
      } = product || {};

      const templateModel = {
        company_name: company.company_name,
        threshold: productThreshold ?? threshold,
        productName: `${productName || ""}${versionCode ? ` (${versionCode})` : ""}`,
        remaining: remaining ?? "",
        utilizationRate: utilizationRate ?? "",
        cardRange: cardRange ?? "",
        timestamp: timestamp ?? new Date().toISOString(),
      };

      try {
        const resp = await client.sendEmailWithTemplate({
          From: POSTMARK_FROM,
          To: to,
          TemplateAlias: LOW_BIN_TEMPLATE,
          TemplateModel: templateModel,
        });
        return { ok: true, resp };
      } catch (err) {
        return { ok: false, error: err?.message || err };
      }
    });

    const results = await Promise.all(sendPromises);
    const successCount = results.filter(r => r.ok).length;
    const failed = results.filter(r => !r.ok);

    return res.status(200).json({
      success: true,
      sent: successCount,
      failedCount: failed.length,
      failed,
    });
  } catch (error) {
    return res.status(500).json({ success: false, message: "Internal Server Error", error: error.message });
  }
}

module.exports = {
  criticalStockEmail,
};