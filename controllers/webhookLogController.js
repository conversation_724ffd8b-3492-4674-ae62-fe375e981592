const WebhookLog = require("../models/WebhookLog");

// GET all webhook logs with pagination and filtering
exports.getWebhookLogs = async (req, res) => {
    try {
        const page = Number.parseInt(req.query.page) || 1;
        const limit = Number.parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;

        // Build filter object
        const filter = {};
        if (req.query.event) {
            filter.event = { $regex: req.query.event, $options: "i" };
        }
        if (req.query.status) {
            filter.responseStatus = Number.parseInt(req.query.status);
        }
        if (req.query.from && req.query.to) {
            filter.createdAt = {
                $gte: new Date(req.query.from),
                $lte: new Date(req.query.to),
            };
        }

        const total = await WebhookLog.countDocuments(filter);
        const logs = await WebhookLog.find(filter).sort({ createdAt: -1 }).skip(skip).limit(limit);

        res.json({
            logs,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit),
            },
        });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
};

