const axios = require('axios');
const { generateJwtToken } = require('../config/LegecyService');

const FEE_SERVICE_URL = process.env.CARD_AUTH_URL;
const CHECK_FEE_PATH =  '/check-tx-fee';
const APPLY_FEE_PATH =  '/apply-tx-fee';

async function checkFee(req, res) {
  try {
    const token = generateJwtToken();
    const payload = {
      OperationType: req.body.OperationType,
      CustomerType: req.body.CustomerType,
      ProductType: req.body.ProductType,
      Location: req.body.Location,
      Modifier: req.body.Modifier,
    };

    const response = await axios.post(
      `${FEE_SERVICE_URL}${CHECK_FEE_PATH}`,
      payload,
      { headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' } }
    );

    return res.status(response.status || 200).json(response.data);
  } catch (error) {
    return res.status(500).json({ error: error.response?.data || error.message });
  }
}

async function applyFee(req, res) {
  try {
    const token = generateJwtToken();

    const payload = {
      OperationType: req.body.OperationType,
      CustomerType: req.body.CustomerType,
      ProductType: req.body.ProductType,
      Location: req.body.Location,
      Modifier: req.body.Modifier,
      transactionAmount: req.body.transactionAmount,
      region: req.body.region,
      retrievalReferenceNumber: req.body.retrievalReferenceNumber || Date.now().toString(),
      IBAN: req.body.IBAN,
      cardID: req.body.cardID,
      mcc: req.body.mcc,
    };

    const response = await axios.post(
      `${FEE_SERVICE_URL}${APPLY_FEE_PATH}`,
      payload,
      { headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' } }
    );

    return res.status(response.status || 200).json(response.data);
  } catch (error) {
    return res.status(500).json({ error: error.response?.data || error.message });
  }
}

module.exports = {
  checkFee,
  applyFee,
};