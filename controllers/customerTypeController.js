const CustomerType = require("../models/CustomerType");

async function createCustomerType(req, res) {
  try {
    const payload = req.body || {};
    const record = new CustomerType(payload);
    const saved = await record.save();
    return res.status(201).json({ success: true, data: saved });
  } catch (err) {
    return res.status(400).json({ success: false, message: err.message });
  }
}

async function getCustomerTypes(req, res) {
  try {
    const items = await CustomerType.find({ deleted_at: null }).sort({ created_at: -1 });
    return res.status(200).json({ success: true, count: items.length, data: items });
  } catch (err) {
    return res.status(500).json({ success: false, message: err.message });
  }
}

async function deleteCustomerType(req, res) {
  try {
    const { id } = req.params;
    const updated = await CustomerType.findByIdAndUpdate(id, { deleted_at: new Date() }, { new: true });
    if (!updated) return res.status(404).json({ success: false, message: "Record not found" });
    return res.status(200).json({ success: true, message: "Record deleted successfully" });
  } catch (err) {
    return res.status(500).json({ success: false, message: err.message });
  }
}

module.exports = {
  createCustomerType,
  getCustomerTypes,
  deleteCustomerType,
};
