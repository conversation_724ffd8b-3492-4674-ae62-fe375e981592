const axios = require('axios');
const { generateJwtToken, fetchBalance } = require('../config/LegecyService');

const ITCARD_BASE_URL =
  process.env.CARD_AUTH_URL

const TRANSACTIONS_PATH = process.env.ITCARD_TRANSACTIONS_PATH || process.env.ITCARD_TRANSACTIONS || '/api/itcard/transactions';

async function transactions(req, res) {
  try {
    const { accountIdentification, dateStart, dateEnd, page, limit, cardId } = req.query;

    let startDate = dateStart;
    let endDate = dateEnd;

    if (!startDate || !endDate) {
      const today = new Date();
      const lastMonth = new Date();
      lastMonth.setMonth(today.getMonth() - 1);
      const formatDate = d => d.toISOString().split('T')[0];
      startDate = formatDate(lastMonth);
      endDate = formatDate(today);
    }

    const token = generateJwtToken();

    const response = await axios.get(`${ITCARD_BASE_URL}${TRANSACTIONS_PATH}`, {
      headers: { Authorization: `Bearer ${token}` },
      params: {
        accountIdentification,
        dateStart: startDate,
        cardId,
        dateEnd: endDate,
        page,
        limit
      }
    });

    return res.status(response.status || 200).json(response.data);
  } catch (error) {
      console.error(error);
    return res.status(500).json({ error: error.response?.data || error.message });
  }
}

async function fetchBalanceByAccount(req, res) {
  try {
    const { accountIdentification } = req.params;
    const data = await fetchBalance(accountIdentification);
    return res.status(200).json(data);
  } catch (error) {
    return res.status(500).json({ error: error.message });
  }
}

module.exports = {
  transactions,
  fetchBalanceByAccount,
};