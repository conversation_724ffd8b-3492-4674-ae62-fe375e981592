const BinUsage = require("../models/BinUsage");
const { createTask, updateTaskByRecordId } = require("../config/EventHandler");
const User = require("../models/user");

/* Helpers */
function getClientIp(req) {
    return req.headers["x-forwarded-for"]?.split(",").shift() || req.socket?.remoteAddress;
}

async function createBinUsage(req, res) {
    try {
        const bin = new BinUsage(req.body);
        const saved = await bin.save();

        try {
            const user = req.body.created_by ? await User.findById(req.body.created_by) : null;
            const ip = getClientIp(req);
            const taskData = {
                refId: saved._id,
                type: 'BIN Usage',
                title: (user?.name || 'Unknown') + ' requested a new BIN Usage "' + (req.body.usage || '') + '"',
                date: new Date(),
                user: user?._id || null,
                ipAddress: ip,
            };
            await createTask(taskData);
        } catch (taskErr) {
            console.error('createTask failed:', taskErr?.message || taskErr);
        }

        return res.status(201).json({ success: true, data: saved });
    } catch (err) {
        return res.status(400).json({ success: false, message: err.message });
    }
}

async function listBinUsages(req, res) {
    try {
        const items = await BinUsage.find({ deleted_at: null }).populate("created_by").sort({ created_at: -1 });
        return res.status(200).json({ success: true, count: items.length, data: items });
    } catch (err) {
        return res.status(400).json({ success: false, message: err.message });
    }
}

async function softDeleteBinUsage(req, res) {
    try {
        const { id } = req.params;
        const updated = await BinUsage.findByIdAndUpdate(id, { deleted_at: new Date() }, { new: true });
        if (!updated) return res.status(404).json({ success: false, message: 'Record not found' });
        return res.json({ success: true, message: 'Record deleted successfully' });
    } catch (err) {
        return res.status(500).json({ success: false, message: 'Error deleting Record', error: err.message });
    }
}

/* Approvals / workflow helpers */
async function approveBinUsage(req, res) {
    try {
        const { entityId } = req.body;
        if (!entityId) return res.status(400).json({ success: false, message: "record ID is required." });

        const record = await BinUsage.findById(entityId);
        if (!record) return res.status(404).json({ success: false, message: "record not found." });

        const currentVersion = parseFloat(record.version) || 0.0;
        const updatedVersion = (currentVersion + 0.1).toFixed(1);

        await BinUsage.findByIdAndUpdate(entityId, { status: "active", version: updatedVersion }, { new: true });
        return res.status(200).json({ success: true, message: "record approved successfully." });
    } catch (err) {
        return res.status(500).json({ success: false, message: "Internal server error.", error: err.message });
    }
}

async function declineBinUsage(req, res) {
    try {
        const { entityId, reason } = req.body;
        if (!entityId || !reason) return res.status(400).json({ success: false, message: "record ID and reason are required." });

        const record = await BinUsage.findById(entityId);
        if (!record) return res.status(404).json({ success: false, message: "record not found." });

        const currentVersion = parseFloat(record.version) || 0.0;
        const updatedVersion = (currentVersion + 0.1).toFixed(1);

        await BinUsage.findByIdAndUpdate(entityId, { status: "decline", reason, version: updatedVersion }, { new: true });
        return res.status(200).json({ success: true, message: "record declined successfully." });
    } catch (err) {
        return res.status(500).json({ success: false, message: "Internal server error.", error: err.message });
    }
}

async function modifyBinUsage(req, res) {
    try {
        const { entityId, instructions } = req.body;
        if (!entityId || !instructions) return res.status(400).json({ success: false, message: "record ID and modification instructions are required." });

        const record = await BinUsage.findById(entityId);
        if (!record) return res.status(404).json({ success: false, message: "record not found." });

        const currentVersion = parseFloat(record.version) || 0.0;
        const updatedVersion = (currentVersion + 0.2).toFixed(1);

        await BinUsage.findByIdAndUpdate(entityId, { status: "modify", reason: instructions, version: updatedVersion }, { new: true });
        return res.status(200).json({ success: true, message: "record modification request submitted successfully." });
    } catch (err) {
        return res.status(500).json({ success: false, message: "Internal server error.", error: err.message });
    }
}

async function updateBinUsage(req, res) {
    try {
        const { id } = req.params;
        const updateData = req.body;
        const actionPerformedBy = req.body.updated_by;

        const record = await BinUsage.findById(id);
        if (!record) return res.status(404).json({ success: false, message: "BIN Usage not found." });

        if (updateData.usage) record.usage = updateData.usage;

        const currentVersion = parseFloat(record.version) || 0.0;
        record.version = (currentVersion + 0.1).toFixed(1);
        record.status = "pending";
        record.updated_by = actionPerformedBy;
        record.updated_at = new Date();

        const updatedRecord = await record.save();

        try {
            await updateTaskByRecordId(id, updatedRecord.usage);
        } catch (taskErr) {
            console.error('updateTaskByRecordId failed:', taskErr?.message || taskErr);
        }

        return res.status(200).json({ success: true, message: "BIN Usage updated successfully.", data: updatedRecord });
    } catch (err) {
        return res.status(500).json({ success: false, message: "Internal server error.", error: err.message });
    }
}

module.exports = {
    createBinUsage,
    listBinUsages,
    softDeleteBinUsage,
    approveBinUsage,
    declineBinUsage,
    modifyBinUsage,
    updateBinUsage,
};