const BinCategory = require('../models/BinCategory');
const { createTask, updateTaskByRecordId } = require("../config/EventHandler");
const User = require("../models/user");

/**
 * Helper: get client IP
 */
function getClientIp(req) {
    return req.headers["x-forwarded-for"]?.split(",").shift() || req.socket?.remoteAddress;
}

/**
 * Create a new BIN category and create a task
 */
async function createBinCategory(req, res) {
    try {
        const bin = new BinCategory(req.body);
        const saved = await bin.save();

        // Try to fetch user and create task
        try {
            const user = await User.findById(req.body.created_by);
            const ip = getClientIp(req);
            const taskData = {
                refId: saved._id,
                type: 'BIN Category',
                title: (user?.name || 'Unknown') + ' requested a new BIN Category "' + (req.body.category || '') + '"',
                date: new Date(),
                user: user?._id || null,
                ipAddress: ip,
            };
            await createTask(taskData);
        } catch (taskErr) {
            // log but don't fail the request
            console.error('createTask failed:', taskErr?.message || taskErr);
        }

        return res.status(201).json({ success: true, data: saved });
    } catch (err) {
        return res.status(400).json({ success: false, message: err.message });
    }
}

/**
 * List BIN categories (not deleted)
 */
async function listBinCategories(req, res) {
    try {
        const bins = await BinCategory.find({ deleted_at: null })
            .populate("created_by")
            .populate("currency")
            .sort({ created_at: -1 });
        return res.json({ success: true, data: bins });
    } catch (err) {
        return res.status(400).json({ success: false, message: err.message });
    }
}

/**
 * Soft delete by setting deleted_at
 */
async function deleteBinCategory(req, res) {
    const { id } = req.params;
    try {
        const updated = await BinCategory.findByIdAndUpdate(
            id,
            { deleted_at: new Date() },
            { new: true }
        );
        if (!updated) return res.status(404).json({ message: 'Record not found' });
        return res.json({ message: 'Record deleted successfully' });
    } catch (error) {
        return res.status(500).json({ message: 'Error deleting Record', error });
    }
}

/**
 * Approve record: set status active and bump version
 */
async function approveBinCategory(req, res) {
    try {
        const { entityId } = req.body;
        if (!entityId) return res.status(400).json({ message: "record ID is required." });

        const record = await BinCategory.findById(entityId);
        if (!record) return res.status(404).json({ message: "record not found." });

        const currentVersion = parseFloat(record.version) || 0.0;
        const updatedVersion = (currentVersion + 0.1).toFixed(1);

        await BinCategory.findByIdAndUpdate(entityId, { status: "active", version: updatedVersion }, { new: true });
        return res.status(200).json({ message: "record approved successfully." });
    } catch (error) {
        return res.status(500).json({ message: "Internal server error." });
    }
}

/**
 * Decline record with reason and bump version
 */
async function declineBinCategory(req, res) {
    try {
        const { entityId, reason } = req.body;
        if (!entityId || !reason) return res.status(400).json({ message: "record ID and reason are required." });

        const record = await BinCategory.findById(entityId);
        if (!record) return res.status(404).json({ message: "record not found." });

        const currentVersion = parseFloat(record.version) || 0.0;
        const updatedVersion = (currentVersion + 0.1).toFixed(1);

        await BinCategory.findByIdAndUpdate(
            entityId,
            { status: "decline", reason: reason, version: updatedVersion },
            { new: true }
        );

        return res.status(200).json({ message: "record declined successfully." });
    } catch (error) {
        return res.status(500).json({ message: "Internal server error." });
    }
}

/**
 * Request modification: attach instructions, bump version
 */
async function modifyBinCategory(req, res) {
    try {
        const { entityId, instructions } = req.body;
        if (!entityId || !instructions) return res.status(400).json({ message: "record ID and modification instructions are required." });

        const record = await BinCategory.findById(entityId);
        if (!record) return res.status(404).json({ message: "record not found." });

        const currentVersion = parseFloat(record.version) || 0.0;
        const updatedVersion = (currentVersion + 0.2).toFixed(1);

        await BinCategory.findByIdAndUpdate(
            entityId,
            { status: "modify", reason: instructions, version: updatedVersion },
            { new: true }
        );

        return res.status(200).json({ message: "record modification request submitted successfully." });
    } catch (error) {
        return res.status(500).json({ message: "Internal server error." });
    }
}

/**
 * Update record (PUT /:id) - update fields, bump version, set pending, update task
 */
async function updateBinCategory(req, res) {
    try {
        const { id } = req.params;
        const updateData = req.body;
        const actionPerformedBy = req.body.updated_by;

        let record = await BinCategory.findById(id);
        if (!record) return res.status(404).json({ message: "BIN Category not found." });

        if (updateData.category) record.category = updateData.category;
        if (updateData.bin_prefix) record.bin_prefix = updateData.bin_prefix;
        if (updateData.currency) record.currency = updateData.currency;

        const currentVersion = parseFloat(record.version) || 0.0;
        record.version = (currentVersion + 0.1).toFixed(1);

        record.status = "pending";
        record.updated_by = actionPerformedBy;
        record.updated_at = new Date();

        const updatedRecord = await record.save();

        // Update associated task (best-effort)
        try {
            await updateTaskByRecordId(id, updatedRecord.category);
        } catch (err) {
            console.error('updateTaskByRecordId failed:', err?.message || err);
        }

        return res.status(200).json({ message: "BIN Category updated successfully.", data: updatedRecord });
    } catch (error) {
        return res.status(500).json({ message: "Internal server error.", error: error.message });
    }
}

module.exports = {
    createBinCategory,
    listBinCategories,
    deleteBinCategory,
    approveBinCategory,
    declineBinCategory,
    modifyBinCategory,
    updateBinCategory,
};