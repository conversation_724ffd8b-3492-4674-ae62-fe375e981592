const CardProgrammeType = require("../models/CardProgrammeType");

/* Create new programme type */
async function createCardProgrammeType(req, res) {
  try {
    const payload = req.body || {};
    const record = new CardProgrammeType(payload);
    const saved = await record.save();
    return res.status(201).json({ success: true, data: saved });
  } catch (err) {
    return res.status(400).json({ success: false, message: err.message });
  }
}

/* List programme types */
async function getCardProgrammeTypes(req, res) {
  try {
    const items = await CardProgrammeType.find({ deleted_at: null }).sort({ created_at: -1 });
    return res.json({ success: true, count: items.length, data: items });
  } catch (err) {
    return res.status(500).json({ success: false, message: err.message });
  }
}

/* Soft delete (set deleted_at) */
async function deleteCardProgrammeType(req, res) {
  try {
    const { id } = req.params;
    const updated = await CardProgrammeType.findByIdAndUpdate(id, { deleted_at: new Date() }, { new: true });
    if (!updated) return res.status(404).json({ success: false, message: "Record not found" });
    return res.json({ success: true, message: "Card Programme deleted successfully" });
  } catch (err) {
    return res.status(500).json({ success: false, message: err.message });
  }
}

/* Approve: set status active and increment version by 0.1 */
async function approve(req, res) {
  try {
    const { entityId } = req.body;
    if (!entityId) return res.status(400).json({ message: "record ID is required." });

    const record = await CardProgrammeType.findById(entityId);
    if (!record) return res.status(404).json({ message: "record not found." });

    const currentVersion = parseFloat(record.version) || 0.0;
    const updatedVersion = (currentVersion + 0.1).toFixed(1);

    await CardProgrammeType.findByIdAndUpdate(entityId, { status: "active", version: updatedVersion }, { new: true });
    return res.status(200).json({ message: "record approved successfully." });
  } catch (err) {
    return res.status(500).json({ message: "Internal server error.", error: err.message });
  }
}

/* Decline: set status decline, save reason, bump version by 0.1 */
async function decline(req, res) {
  try {
    const { entityId, reason } = req.body;
    if (!entityId || !reason) return res.status(400).json({ message: "record ID and reason are required." });

    const record = await CardProgrammeType.findById(entityId);
    if (!record) return res.status(404).json({ message: "record not found." });

    const currentVersion = parseFloat(record.version) || 0.0;
    const updatedVersion = (currentVersion + 0.1).toFixed(1);

    await CardProgrammeType.findByIdAndUpdate(
      entityId,
      { status: "decline", reason, version: updatedVersion },
      { new: true }
    );
    return res.status(200).json({ message: "record declined successfully." });
  } catch (err) {
    return res.status(500).json({ message: "Internal server error.", error: err.message });
  }
}

/* Modify: set status modify, save instructions, bump version by 0.2 */
async function modify(req, res) {
  try {
    const { entityId, instructions } = req.body;
    if (!entityId || !instructions) return res.status(400).json({ message: "record ID and modification instructions are required." });

    const record = await CardProgrammeType.findById(entityId);
    if (!record) return res.status(404).json({ message: "record not found." });

    const currentVersion = parseFloat(record.version) || 0.0;
    const updatedVersion = (currentVersion + 0.2).toFixed(1);

    await CardProgrammeType.findByIdAndUpdate(
      entityId,
      { status: "modify", reason: instructions, version: updatedVersion },
      { new: true }
    );
    return res.status(200).json({ message: "record modification request submitted successfully." });
  } catch (err) {
    return res.status(500).json({ message: "Internal server error.", error: err.message });
  }
}

module.exports = {
  createCardProgrammeType,
  getCardProgrammeTypes,
  deleteCardProgrammeType,
  approve,
  decline,
  modify,
};
