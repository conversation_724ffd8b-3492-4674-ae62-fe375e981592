const CompanyContact = require("../models/Contact");

async function createContact(req, res) {
    try {
        const contact = await CompanyContact.create(req.body);
        return res.status(201).json(contact);
    } catch (error) {
        return res.status(400).json({ error: error.message });
    }
}

async function listContacts(req, res) {
    try {
        const contacts = await CompanyContact.find().populate("company");
        return res.status(200).json(contacts);
    } catch (error) {
        return res.status(500).json({ error: error.message });
    }
}

async function getContactById(req, res) {
    try {
        const contact = await CompanyContact.findById(req.params.id).populate("company");
        if (!contact) return res.status(404).json({ error: "Contact not found" });
        return res.status(200).json(contact);
    } catch (error) {
        return res.status(500).json({ error: error.message });
    }
}

async function updateContact(req, res) {
    try {
        const contact = await CompanyContact.findByIdAndUpdate(req.params.id, req.body, {
            new: true,
            runValidators: true,
        });
        if (!contact) return res.status(404).json({ error: "Contact not found" });
        return res.status(200).json(contact);
    } catch (error) {
        return res.status(400).json({ error: error.message });
    }
}

async function deleteContact(req, res) {
    try {
        const contact = await CompanyContact.findByIdAndDelete(req.params.id);
        if (!contact) return res.status(404).json({ error: "Contact not found" });
        return res.status(200).json({ message: "Contact deleted successfully" });
    } catch (error) {
        return res.status(500).json({ error: error.message });
    }
}

module.exports = {
    createContact,
    listContacts,
    getContactById,
    updateContact,
    deleteContact,
};