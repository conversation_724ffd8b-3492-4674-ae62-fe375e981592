const Region = require("../models/Region")

// Controller functions for handling region operations

/**
 * Create a new region
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createRegion = async (req, res) => {
    try {
        const { name, number, geographicRegion, countries, deliveryMethods } = req.body

        // Create new region
        const region = await Region.create({
            name,
            number,
            geographicRegion,
            countries,
            deliveryMethods: deliveryMethods || ["Polish Post"], // Default to Polish Post if not provided
        })

        res.status(201).json({
            success: true,
            data: region,
        })
    } catch (error) {
        // Handle validation errors
        if (error.name === "ValidationError") {
            const messages = Object.values(error.errors).map((err) => err.message)

            return res.status(400).json({
                success: false,
                error: messages,
            })
        }


        if (error.code === 11000) {
            return res.status(400).json({
                success: false,
                error: "Region number already exists",
            })
        }

        res.status(500).json({
            success: false,
            error: "Server Error",
        })
    }
}

/**
 * Get all regions
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getRegions = async (req, res) => {
    try {
        const regions = await Region.find()

        res.status(200).json({
            success: true,
            count: regions.length,
            data: regions,
        })
    } catch (error) {
        res.status(500).json({
            success: false,
            error: "Server Error",
        })
    }
}

/**
 * Get a single region by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getRegion = async (req, res) => {
    try {
        const region = await Region.findById(req.params.id)

        if (!region) {
            return res.status(404).json({
                success: false,
                error: "Region not found",
            })
        }

        res.status(200).json({
            success: true,
            data: region,
        })
    } catch (error) {
        res.status(500).json({
            success: false,
            error: "Server Error",
        })
    }
}

/**
 * Update a region
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateRegion = async (req, res) => {
    try {
        const region = await Region.findByIdAndUpdate(req.params.id, req.body, {
            new: true,
            runValidators: true,
        })

        if (!region) {
            return res.status(404).json({
                success: false,
                error: "Region not found",
            })
        }

        res.status(200).json({
            success: true,
            data: region,
        })
    } catch (error) {
        // Handle validation errors
        if (error.name === "ValidationError") {
            const messages = Object.values(error.errors).map((err) => err.message)

            return res.status(400).json({
                success: false,
                error: messages,
            })
        }

        res.status(500).json({
            success: false,
            error: "Server Error",
        })
    }
}

/**
 * Delete a region
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteRegion = async (req, res) => {
    try {
        const region = await Region.findByIdAndDelete(req.params.id)

        if (!region) {
            return res.status(404).json({
                success: false,
                error: "Region not found",
            })
        }

        res.status(200).json({
            success: true,
            data: {},
        })
    } catch (error) {
        res.status(500).json({
            success: false,
            error: "Server Error",
        })
    }
}
