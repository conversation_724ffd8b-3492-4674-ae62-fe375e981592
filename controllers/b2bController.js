const B2BAccount = require("../models/B2BAccount");
const B2BIndividualAccounts = require("../models/B2BIndividualAccounts");
const { sendPostRequest } = require("../config/ApiInstense");
const { registerUser } = require("../controllers/userController");
const Account = require("../models/Account");
const sendCardCreationWebhook = require("../config/webhook");
const Card = require("../models/B2BCard");
const IndividualOnboarding = require("../models/IndividualOnboarding");
const mongoose = require("mongoose");

async function listAccounts(req, res) {
    try {
        const accounts = await B2BAccount.find().populate("parentCompany", "company_name").sort({ createdAt: -1 });
        return res.status(200).json({ success: true, count: accounts.length, data: accounts });
    } catch (error) {
        return res.status(500).json({ success: false, message: "Server error", error: error.message });
    }
}

async function getAccount(req, res) {
    try {
        const account = await B2BAccount.findById(req.params.id).populate("parentCompany").populate("products");
        if (!account) return res.status(404).json({ success: false, message: "B2B account not found" });
        const debitAccount = await Account.findOne({ b2bCompany: req.params.id });
        return res.status(200).json({ success: true, data: account, debitAccount });
    } catch (error) {
        return res.status(500).json({ success: false, message: "Server error", error: error.message });
    }
}

async function createAccount(req, res) {
    let savedAccount = null;
    try {
        const {
            companyName, clientCode, phoneNumber, authPhoneNumber, email, address,
            nip, regon, embossedName, parentCompany,
        } = req.body;

        if (!companyName || !clientCode || !email || !address) {
            return res.status(400).json({
                success: false,
                message: "Missing required fields: companyName, clientCode, email, and address are required.",
            });
        }

        const addressArray = [
            { ...address, type: "registration_address" },
            { ...address, type: "delivery_address" }
        ];

        const newAccount = new B2BAccount({
            companyName, clientCode, phoneNumber, authPhoneNumber, email,
            addresses: addressArray, nip, regon, embossedName, parentCompany,
        });

        savedAccount = await newAccount.save();

        const clientPayload = {
            companyName, clientCode, phoneNumber, authPhoneNumber, email,
            address: {
                street: address.street,
                buildingNumber: address.buildingNumber,
                apartmentNumber: address.apartmentNumber,
                city: address.city,
                zipCode: address.zipCode,
                country: address.country,
            },
            nip, regon, embossedName, company: true, customer: false,
        };

        let result;
        try {
            result = await sendPostRequest(
                "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/clients",
                clientPayload
            );
        } catch (apiError) {
            if (savedAccount?._id) await B2BAccount.findByIdAndDelete(savedAccount._id);
            return res.status(502).json({
                success: false,
                message: "External API request failed",
                error: apiError.message || "Could not reach client API",
            });
        }

        if (result.status?.toLowerCase() === "approved") {
            await registerUser(companyName, email, [], "active", "corporate", savedAccount._id);
            return res.status(201).json({
                success: true,
                message: "B2B account created successfully",
                data: savedAccount,
                apiResponse: result,
            });
        } else {
            await B2BAccount.findByIdAndDelete(savedAccount._id);
            return res.status(400).json({
                success: false,
                message: "External API rejected the account creation",
                error: result,
            });
        }
    } catch (error) {
        if (savedAccount?._id) await B2BAccount.findByIdAndDelete(savedAccount._id);

        if (error.name === "ValidationError") {
            const messages = Object.values(error.errors).map((val) => val.message);
            return res.status(400).json({ success: false, message: "Validation failed", errors: messages });
        }

        if (error.code === 11000) {
            const duplicateField = Object.keys(error.keyValue)[0];
            return res.status(400).json({ success: false, message: `Duplicate entry: ${duplicateField} already exists.` });
        }

        return res.status(500).json({ success: false, message: "Unexpected error occurred while creating B2B account", error: error.message });
    }
}

async function updateAccount(req, res) {
    try {
        const updatedAccount = await B2BAccount.findByIdAndUpdate(
            req.params.id,
            { ...req.body, updatedAt: Date.now() },
            { new: true, runValidators: true },
        );

        if (!updatedAccount) return res.status(404).json({ success: false, message: "B2B account not found" });

        return res.status(200).json({ success: true, message: "B2B account updated successfully", data: updatedAccount });
    } catch (error) {
        if (error.name === "ValidationError") {
            const messages = Object.values(error.errors).map((val) => val.message);
            return res.status(400).json({ success: false, message: "Validation error", errors: messages });
        }
        return res.status(500).json({ success: false, message: "Server error", error: error.message });
    }
}

async function deleteAccount(req, res) {
    try {
        const deletedAccount = await B2BAccount.findByIdAndDelete(req.params.id);
        if (!deletedAccount) return res.status(404).json({ success: false, message: "B2B account not found" });
        return res.status(200).json({ success: true, message: "B2B account deleted successfully" });
    } catch (error) {
        return res.status(500).json({ success: false, message: "Server error", error: error.message });
    }
}

async function updatePermissions(req, res) {
    try {
        const { id } = req.params;
        const { permissions, permissionAudit } = req.body;

        const errors = [];
        if (!Array.isArray(permissions)) errors.push({ msg: "Permissions must be an array" });
        if (!Array.isArray(permissionAudit)) errors.push({ msg: "Permission audit must be an array" });
        if (errors.length) return res.status(400).json({ errors });

        const currentCompany = await B2BAccount.findById(id);
        if (!currentCompany) return res.status(404).json({ success: false, message: 'Company not found' });

        const oldPermissions = currentCompany.permissions || [];
        const addedPermissions = permissions.filter(p => !oldPermissions.includes(p));
        const removedPermissions = oldPermissions.filter(p => !permissions.includes(p));

        let changeDetails = [];
        if (addedPermissions.length) changeDetails.push(`Added: ${addedPermissions.join(", ")}`);
        if (removedPermissions.length) changeDetails.push(`Removed: ${removedPermissions.join(", ")}`);
        if (!addedPermissions.length && !removedPermissions.length) changeDetails.push("No changes in permissions");

        currentCompany.permissions = permissions;
        currentCompany.permissionAudit = permissionAudit;
        currentCompany.permissionsLog.push({
            action: "Permission Update",
            details: changeDetails.join(" | "),
            username: req.user?.name || "Unknown User",
            email: req.user?.email || "Unknown Email"
        });

        await currentCompany.save();

        return res.status(200).json({ success: true, message: 'Permissions updated successfully', changes: changeDetails });
    } catch (error) {
        return res.status(400).json({ success: false, message: error.message || 'An error occurred' });
    }
}

async function createPhysicalCard(req, res) {
    try {
        const {
            b2bClientId, phoneNumber,
            nickname, productCode, deliveryMethod, userId
        } = req.body;

        const individual = await IndividualOnboarding.findById(userId).populate("b2bClient");
        const account = await B2BIndividualAccounts.findOne({ parent: individual._id });

        if (!account) return res.status(404).json({ success: false, message: "Account not found for the provided b2bClientId" });

        const cardData = {
            holder: account.owners[0].clientCode,
            embossName2: individual.b2bClient.clientCode.split("-")[1] || "",
            productCode,
            visual: productCode,
            delivery: { deliveryType: "LETTER" },
            pinDelivery: { deliveryType: "NONE" },
            extAppId: "testextAppId",
            account: {
                accNo: "**********************",
                currencyCode: account.currencyCode,
                productCode: "RYV1",
                owners: account.owners.map(owner => ({
                    clientCode: owner.clientCode,
                    relationship: owner.relationship || "OWN"
                }))
            },
            "3dsAuthMethods": ["MOBILE_APP"]
        };

        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/debitCard";
        const result = await sendPostRequest(url, cardData);

        if (!result.cardKey) return res.status(400).json({ success: false, message: "Card creation failed", error: result });

        const DbCardData = {
            cardHash: result.cardHash, cardKey: result.cardKey, expDate: result.expDate,
            status: result.status, statusCode: result.statusCode, kind: result.kind,
            productCode: result.productCode, productDesc: result.productDesc, main: result.main,
            holder: result.holder, accNo: result.accNo, embossName1: result.embossName1,
            cardMask: result.cardMask, parent: b2bClientId, authPhoneNumber: phoneNumber,
            cardholder: individual._id, nickName: nickname, deliveryMethod
        };

        const newCard = new Card(DbCardData);
        await newCard.save();

        await sendCardCreationWebhook({ ...result, deliveryMethod });

        return res.status(201).json({ success: true, message: "Physical card created successfully", data: newCard });
    } catch (error) {
        return res.status(500).json({ success: false, message: "Internal Server Error", error: error.message });
    }
}

async function createVirtualCard(req, res) {
    try {
        const {  clientId,  b2bClientId, nickname, productCode } = req.body;

        const individual = await IndividualOnboarding.findOne({ clientID: clientId }).populate("b2bClient");
        const account = await B2BIndividualAccounts.findOne({ parent: individual._id });
        if (!account) return res.status(404).json({ success: false, message: "Account not found for the provided clientId" });

        const cardData = {
            holder: account.owners[0].clientCode,
            embossName2: individual.b2bClient.clientCode.split("-")[1] || "",
            productCode: productCode,
            visual: productCode,
            delivery: { deliveryType: "LETTER" },
            pinDelivery: { deliveryType: "NONE" },
            extAppId: "testextAppId",
            account: {
                accNo: "**********************",
                currencyCode: account.currencyCode,
                productCode: "RYV1",
                owners: account.owners.map(owner => ({
                    clientCode: owner.clientCode,
                    relationship: owner.relationship || "OWN"
                }))
            },
            "3dsAuthMethods": ["MOBILE_APP"]
        };

        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/debitCard";
        const result = await sendPostRequest(url, cardData);

        if (!result.cardKey) return res.status(400).json({ success: false, message: "Card creation failed", error: result });

        const DbCardData = {
            cardHash: result.cardHash, cardKey: result.cardKey, expDate: result.expDate,
            status: result.status, statusCode: result.statusCode, kind: result.kind,
            productCode: result.productCode, productDesc: result.productDesc, main: result.main,
            holder: result.holder, accNo: result.accNo, embossName1: result.embossName1,
            cardMask: result.cardMask, parent: b2bClientId, cardholder: individual._id, nickName: nickname,
        };

        const newCard = new Card(DbCardData);
        await newCard.save();

        await sendCardCreationWebhook({ ...result });

        return res.status(201).json({ success: true, message: "Virtual card created successfully", data: newCard });
    } catch (error) {
        return res.status(500).json({ success: false, message: "Internal Server Error", error: error.message });
    }
}

async function getCardsByParent(req, res) {
    try {
        const cards = await Card.find({ parent: req.params.parentId });
        if (!cards || cards.length === 0) return res.status(404).json({ success: false, message: "No cards found for this parent ID" });
        return res.status(200).json({ success: true, count: cards.length, data: cards });
    } catch (error) {
        return res.status(500).json({ success: false, message: "Failed to fetch cards by parent ID", error: error.message });
    }
}

async function getCardById(req, res) {
    try {
        const card = await Card.findById(req.params.id);
        if (!card) return res.status(404).json({ success: false, message: "Card found" });
        return res.status(200).json({ success: true, data: card });
    } catch (error) {
        return res.status(500).json({ success: false, message: "Failed to fetch card", error: error.message });
    }
}

async function getCardholders(req, res) {
    try {
        const accounts = await IndividualOnboarding.find({ b2bClient: req.params.id });
        if (!accounts) return res.status(404).json({ success: false, message: "Accounts not found" });
        return res.status(200).json({ success: true, data: accounts });
    } catch (error) {
        return res.status(500).json({ success: false, message: "Failed to fetch cardholders", error: error.message });
    }
}

async function getCardholderCards(req, res) {
    try {
        const cards = await Card.find({ cardholder: req.params.id });
        return res.status(200).json({ success: true, data: cards });
    } catch (error) {
        return res.status(500).json({ success: false, message: "Failed to fetch cards", error: error.message });
    }
}

async function assignProducts(req, res) {
    try {
        const { productIds } = req.body;
        if (!Array.isArray(productIds) || productIds.length === 0) {
            return res.status(400).json({ success: false, message: "No product IDs provided." });
        }

        const validIds = productIds.filter(id => mongoose.Types.ObjectId.isValid(id));
        if (validIds.length === 0) return res.status(400).json({ success: false, message: "No valid product IDs provided." });

        const updatedAccount = await B2BAccount.findByIdAndUpdate(
            req.params.id,
            { $addToSet: { products: { $each: validIds } }, updatedAt: Date.now() },
            { new: true }
        ).populate("products");

        if (!updatedAccount) return res.status(404).json({ success: false, message: "B2B account not found" });

        return res.status(200).json({ success: true, message: "Products assigned successfully", data: updatedAccount });
    } catch (error) {
        return res.status(500).json({ success: false, message: "Internal server error", error: error.message });
    }
}

async function getProducts(req, res) {
    try {
        const account = await B2BAccount.findById(req.params.id).populate("products");
        if (!account) return res.status(404).json({ success: false, message: "B2B account not found" });
        return res.status(200).json({ success: true, account, products: account.products });
    } catch (error) {
        return res.status(500).json({ success: false, message: "Failed to fetch assigned products", error: error.message });
    }
}

async function unassignProduct(req, res) {
    try {
        const { id, productId } = req.params;
        if (!mongoose.Types.ObjectId.isValid(id) || !mongoose.Types.ObjectId.isValid(productId)) {
            return res.status(400).json({ success: false, message: "Invalid B2B account ID or Product ID." });
        }

        const updatedAccount = await B2BAccount.findByIdAndUpdate(
            id,
            { $pull: { products: productId }, updatedAt: Date.now() },
            { new: true }
        ).populate("products");

        if (!updatedAccount) return res.status(404).json({ success: false, message: "B2B account not found" });

        return res.status(200).json({ success: true, message: "Product unassigned successfully", data: updatedAccount });
    } catch (error) {
        return res.status(500).json({ success: false, message: "Internal server error", error: error.message });
    }
}

async function updateAddress(req, res) {
    try {
        const {
            onboardingId, building, street, apartment, city, 
            postalCode, country,  
        } = req.body;

        const client = await IndividualOnboarding.findById(onboardingId);
        if (!client) return res.status(404).json({ success: false, message: "Client not found" });

        client.address = {
            street,
            buildingNumber: building,
            apartmentNumber: apartment || "",
            city,
            country,
            zipCode: postalCode,
        };

        await client.save();

        return res.status(200).json({ success: true, message: "Address updated successfully", data: client.addresses });
    } catch (error) {
        return res.status(500).json({ success: false, message: "Internal server error", error: error.message });
    }
}

module.exports = {
    listAccounts,
    getAccount,
    createAccount,
    updateAccount,
    deleteAccount,
    updatePermissions,
    createPhysicalCard,
    createVirtualCard,
    getCardsByParent,
    getCardById,
    getCardholders,
    getCardholderCards,
    assignProducts,
    getProducts,
    unassignProduct,
    updateAddress,
};