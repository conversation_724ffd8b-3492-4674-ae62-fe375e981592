const BinRange = require("../models/BinRange");
const User = require("../models/user");
const { ApiError } = require('../config/ApiError');
const mongoose = require("mongoose");
const { createTask } = require("../config/EventHandler");


/**
 * Create a new bin range
 */
async function createBinRange(req, res) {
    try {
        const { binType, binCode, binCodePrefix, binCodeSuffix, currency, bin_start, bin_end, created_by } = req.body

        // // Check for overlapping bin ranges

        // Create new bin range
        const binRange = new BinRange({
            binType,
            binCode,
            binCodePrefix,
            binCodeSuffix,
            currency,
            bin_start,
            bin_end,
            created_by: created_by || req.user._id,
            status: "pending", // New entries start as pending
        })

        var p = await binRange.save()
        const user = await User.findById(req.body.created_by);

        const taskData = {
            refId: p._id,
            type: 'Bin Range',
            title: user.name + ' requested a new Bin Range "' + p.binCode + '"',
            date: new Date(),
            user: user._id,
            ipAddress: '************',
        };
        await createTask(taskData)
        return res.status(201).json({ success: true, data: binRange });
    } catch (error) {
        return res.status(400).json({ success: false, message: error.message });
    }
}

/**
 * Get all bin ranges
 */
async function getAllBinRanges(req, res) {
    try {
        const binRanges = await BinRange.find()
            .populate("binType", "type")
            .populate("currency", "currency_code")
            .populate("created_by", "name")
            .sort({ created_at: -1 })

        return res.status(200).json({ success: true, count: binRanges.length, data: binRanges });
    } catch (error) {
        return res.status(500).json({ success: false, message: error.message });
    }
}

/**
 * Get a single bin range by ID
 */
async function getBinRangeById(req, res) {
    try {
        const { id } = req.params

        if (!mongoose.Types.ObjectId.isValid(id)) {
            return next(new ApiError(400, "Invalid bin range ID"))
        }

        const binRange = await BinRange.findById(id)
            .populate("binType", "type")
            .populate("currency", "currency_code")
            .populate("created_by", "name")

        if (!binRange) {
            return next(new ApiError(404, "Bin range not found"))
        }

        return res.status(200).json({ success: true, data: binRange });
    } catch (error) {
        return res.status(500).json({ success: false, message: error.message });
    }
}

/**
 * Update a bin range
 */
async function updateBinRange(req, res) {
    try {
        const { id } = req.params
        const updateData = req.body

        if (!mongoose.Types.ObjectId.isValid(id)) {
            return next(new ApiError(400, "Invalid bin range ID"))
        }

        // Check if bin range is being updated
        if (updateData.bin_start && updateData.bin_end) {
            const overlappingRanges = await BinRange.findOverlappingRanges(updateData.bin_start, updateData.bin_end, id)

            if (overlappingRanges.length > 0) {
                return next(new ApiError(400, "The updated bin range overlaps with existing ranges"))
            }
        }

        // Set status to 'modify' when updating an existing record
        if (updateData.status === "active") {
            updateData.status = "modify"
        }

        // Add updated_by and updated_at
        updateData.updated_by = req.user._id
        updateData.updated_at = new Date()

        const binRange = await BinRange.findByIdAndUpdate(id, updateData, { new: true, runValidators: true })
            .populate("binType", "type")
            .populate("currency", "currency_code")
            .populate("created_by", "name")

        if (!binRange) {
            return next(new ApiError(404, "Bin range not found"))
        }

        return res.status(200).json({ success: true, data: binRange });
    } catch (error) {
        return res.status(400).json({ success: false, message: error.message });
    }
}

/**
 * Delete a bin range (soft delete by changing status)
 */
async function deleteBinRange(req, res) {
    try {
        const { id } = req.params

        if (!mongoose.Types.ObjectId.isValid(id)) {
            return next(new ApiError(400, "Invalid bin range ID"))
        }

        // Instead of actually deleting, we change the status to 'pending' for approval
        const binRange = await BinRange.findByIdAndDelete(
            id
        )

        if (!binRange) {
            return next(new ApiError(404, "Bin range not found"))
        }

        return res.status(200).json({ success: true, message: "Bin range deleted successfully" });
    } catch (error) {
        return res.status(500).json({ success: false, message: error.message });
    }
}

/**
 * Approve record: set status active and bump version
 */
async function approveBinRange(req, res) {
    try {
        const { entityId } = req.body;
        if (!entityId) return res.status(400).json({ success: false, message: "record ID is required." });

        const record = await BinRange.findById(entityId);
        if (!record) return res.status(404).json({ success: false, message: "record not found." });

        const currentVersion = parseFloat(record.version) || 0.0;
        const updatedVersion = (currentVersion + 0.1).toFixed(1);

        await BinRange.findByIdAndUpdate(entityId, { status: "active", version: updatedVersion }, { new: true });

        return res.status(200).json({ success: true, message: "record approved successfully." });
    } catch (error) {
        return res.status(500).json({ success: false, message: error.message });
    }
}

module.exports = {
    createBinRange,
    getAllBinRanges,
    getBinRangeById,
    updateBinRange,
    deleteBinRange,
    approveBinRange,
};
