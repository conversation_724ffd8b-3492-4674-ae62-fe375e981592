const IndividualOnboarding = require('../models/IndividualOnboarding');

async function updatePermissions(req, res) {
    try {
        const { id } = req.params;
        const { permissions, permissionAudit } = req.body;

        const currentCompany = await IndividualOnboarding.findById(id);
        if (!currentCompany) {
            return res.status(404).json({ success: false, message: 'Account not found' });
        }

        const oldPermissions = currentCompany.permissions || [];
        const addedPermissions = permissions.filter(p => !oldPermissions.includes(p));
        const removedPermissions = oldPermissions.filter(p => !permissions.includes(p));

        let changeDetails = [];
        if (addedPermissions.length) changeDetails.push(`Added: ${addedPermissions.join(", ")}`);
        if (removedPermissions.length) changeDetails.push(`Removed: ${removedPermissions.join(", ")}`);
        if (!addedPermissions.length && !removedPermissions.length) changeDetails.push("No changes in permissions");

        currentCompany.permissions = permissions;
        currentCompany.permissionAudit = permissionAudit;

        currentCompany.permissionsLog = currentCompany.permissionsLog || [];
        currentCompany.permissionsLog.push({
            action: "Permission Update",
            details: changeDetails.join(" | "),
            username: req.user?.name || "Unknown User",
            email: req.user?.email || "Unknown Email",
            at: new Date()
        });

        await currentCompany.save();

        return res.status(200).json({
            success: true,
            message: 'Permissions updated successfully',
            changes: changeDetails
        });
    } catch (error) {
        return res.status(400).json({ success: false, message: error.message || 'An error occurred' });
    }
}

module.exports = {
    updatePermissions,
};