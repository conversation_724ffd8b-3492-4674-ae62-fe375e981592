const Account = require('../models/Account');
const Card = require('../models/AccountCard');
const IndividualOnboarding = require('../models/IndividualOnboarding');
const ProductVersion = require('../models/productVersions');
const WebhookLog = require('../models/WebhookLog');

const { sendPostRequest, sendGetRequest, sendPutRequest } = require('../config/ApiInstense');
const sendCardCreationWebhook = require('../config/webhook');
const { country_currency } = require('../config/currencies');
const { generateJwtToken } = require('../config/LegecyService');
const axios = require('axios');

const IT_CARD_API_URL = process.env.IT_CARD_API_URL;
const CARD_WEBHOOK_URL = process.env.CARD_WEBHOOK_URL;

if (!IT_CARD_API_URL || !CARD_WEBHOOK_URL) {
  throw new Error('Required environment variables IT_CARD_API_URL or CARD_WEBHOOK_URL are not defined');
}

async function createAccount(req, res) {
  try {
    const { clientId, userId } = req.body;
    const onboardingData = await IndividualOnboarding.findOne({ clientID: clientId }).populate('productVersion');
    const code = onboardingData?.productVersion?.find(v => v.version_name.toLowerCase().includes('vtl'))?.version_code;

    const requestBody = {
      productCode: code,
      currencyCode:    process.env.DEFAULT_CURRENCY_CODE,
      accNo: process.env.DEFAULT_ACCOUNT_NUMBER,
      owners: [{ clientCode: clientId, relationship: 'OWN' }]
    };

    const url = `${IT_CARD_API_URL}/accounts/debitAccount`;
    const result = await sendPostRequest(url, JSON.stringify(requestBody));

    if (result?.accNo) {
      const accountData = {
        onboarding: userId,
        accountNumber: result.accNo,
        status: result.status,
        currencyCode: result.currencyCode,
        currencyName: result.currencyName,
        owners: result.owners.map(owner => ({
          clientCode: clientId,
          relationship: owner.relationship,
          mainOwner: owner.mainOwner
        }))
      };

      await new Account(accountData).save();
      return res.status(200).json({ success: true, message: 'Account created', account: result });
    }

    return res.status(500).json({ success: false, message: 'Account creation failed', error: result || 'unknown' });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Error creating account', error: error.message });
  }
}

async function createVirtualCard(req, res) {
  try {
    let { clientId, currencyCode, accNo, nickname, userId, productCode } = req.body;

    const requestBody = {
      holder: clientId,
      productCode,
      visual: productCode,
      delivery: { deliveryType: 'LETTER' },
      pinDelivery: { deliveryType: 'LETTER' },
      account: {
        currencyCode: currencyCode?.toString(),
        productCode,
        owners: [{ clientCode: clientId, relationship: 'OWN' }],
        accNo
      },
      '3dsAuthMethods': ['MOBILE_APP']
    };

    const url = `${IT_CARD_API_URL}/cards/debitCard`;
    const apiResponse = await sendPostRequest(url, JSON.stringify(requestBody));

    const cardData = {
      cardHash: apiResponse.cardHash,
      cardKey: apiResponse.cardKey,
      expDate: apiResponse.expDate,
      status: apiResponse.status,
      statusCode: apiResponse.statusCode,
      kind: apiResponse.kind,
      productCode: apiResponse.productCode,
      productDesc: apiResponse.productDesc,
      main: apiResponse.main,
      holder: apiResponse.holder,
      accNo: apiResponse.accNo,
      embossName1: apiResponse.embossName1,
      cardMask: apiResponse.cardMask,
      onboarding: userId,
      nickName: nickname
    };

    await new Card(cardData).save();
    await sendCardCreationWebhook(apiResponse);

    return res.status(200).json({ success: true, message: 'Virtual Card created', account: cardData });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Error creating virtual card', error: error.message });
  }
}

async function createPhysicalCard(req, res) {
  try {
    const { clientId, accNo, nickname, deliveryMethod, userId, productCode, phoneNumber, country } = req.body;

    const version = await ProductVersion.findOne({ version_code: productCode });
    const currencyCode = country_currency.find(v =>
      v.alphabeticcode === version?.version_name?.slice(-7, -4))?.numericcode;

    const requestBody = {
      holder: clientId,
      productCode,
      visual: productCode,
      delivery: { deliveryType: 'LETTER' },
      pinDelivery: { deliveryType: 'LETTER' },
      account: {
        currencyCode,
        productCode,
        owners: [{ clientCode: clientId, relationship: 'OWN' }],
        accNo
      },
      '3dsAuthMethods': ['MOBILE_APP']
    };

    const url = `${IT_CARD_API_URL}/cards/debitCard`;
    const apiResponse = await sendPostRequest(url, JSON.stringify(requestBody));

    const cardData = {
      cardHash: apiResponse.cardHash,
      cardKey: apiResponse.cardKey,
      expDate: apiResponse.expDate,
      status: apiResponse.status,
      statusCode: apiResponse.statusCode,
      kind: apiResponse.kind,
      productCode: apiResponse.productCode,
      productDesc: apiResponse.productDesc,
      main: apiResponse.main,
      holder: apiResponse.holder,
      accNo: apiResponse.accNo,
      embossName1: apiResponse.embossName1,
      cardMask: apiResponse.cardMask,
      onboarding: userId,
      authPhoneNumber: phoneNumber,
      nickName: nickname,
      deliveryMethod
    };

    await new Card(cardData).save();
    await sendCardCreationWebhook(apiResponse);

    // apply fee if deliveryMethod contains price
    if (deliveryMethod?.price) {
      await applyFee('Consumer', 'BG', 'DHL', country, apiResponse.accNo, apiResponse.cardKey, deliveryMethod.price);
    }

    return res.status(200).json({ success: true, message: 'Physical Card created', account: cardData });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Error creating physical card', error: error.message });
  }
}

async function applyFee(customerType, location, modifier, region, iban, cardID, fee) {
  const token = generateJwtToken();
  const webhookUrl = `${CARD_WEBHOOK_URL}/fee-transaction`;

  const payload = {
    feeType: '1',
    feeNarrative: 'Card Delivery Fee',
    feeAmount: fee,
    FXFeeAmount: '0',
    currency: 'EUR',
    transactionAmount: '0',
    region,
    retrievalReferenceNumber: Date.now().toString(),
    IBAN: iban,
    cardID,
    mcc: '4215'
  };

  try {
    const response = await axios.post(webhookUrl, JSON.stringify(payload), {
      headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' }
    });

    await WebhookLog.create({
      event: 'fee',
      webhookUrl,
      payloadSent: payload,
      responseStatus: response.status,
      responseBody: response.data,
      error: null
    });

    return response.data;
  } catch (error) {
    await WebhookLog.create({
      event: 'fee',
      webhookUrl,
      payloadSent: payload,
      responseStatus: error.response?.status || 500,
      responseBody: error.response?.data || null,
      error: error.message || error.toString()
    });
    throw error;
  }
}

async function getCardById(req, res) {
  try {
    const { id } = req.params;
    const url = `${IT_CARD_API_URL}/cards/${id}`;
    const apiResponse = await sendGetRequest(url);
    const dbCard = await Card.findOne({ cardKey: id }).populate('onboarding');
    return res.status(200).json({ success: true, card: apiResponse, dbCard });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Error fetching card details', error: error.message });
  }
}

async function getClient(req, res) {
  try {
    const { id } = req.params;
    const url = `${IT_CARD_API_URL}/clients/${id}`;
    const apiResponse = await sendGetRequest(url);
    return res.status(200).json({ success: true, client: apiResponse });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Error fetching client', error: error.message });
  }
}

async function addAddress(req, res) {
  try {
    const { clientId, building, street, apartment, city, stateProvince, postalCode, country } = req.body;
    const existingClient = await IndividualOnboarding.findOne({ clientID: clientId });
    if (!existingClient) return res.status(404).json({ error: 'Client not found' });

    const addressUpdate = {
      street,
      buildingNumber: building,
      apartmentNumber: apartment || '',
      stateProvince,
      city,
      country,
      zipCode: postalCode
    };

    existingClient.address = { ...existingClient.address?.toObject?.() || {}, ...addressUpdate };
    await existingClient.save();

    const url = `${IT_CARD_API_URL}/clients/${clientId}/address`;
    const apiResponse = await sendPutRequest(url, JSON.stringify({ address: addressUpdate }));

    if (apiResponse.status !== 'APPROVED') {
      return res.status(500).json({ success: false, message: 'Address change failed', apiResponse });
    }

    return res.status(200).json({ success: true, message: 'Address added successfully' });
  } catch (e) {
    return res.status(500).json({ success: false, message: 'Error adding address', error: e.message });
  }
}

module.exports = {
  createAccount,
  createVirtualCard,
  createPhysicalCard,
  getCardById,
  getClient,
  addAddress
};
