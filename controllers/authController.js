const User = require("../models/user");
const bcrypt = require("bcrypt");
const speakeasy = require("speakeasy");
const {
    generateAccessToken,
    generateRefreshToken,
    verifyAccessToken,
    verifyRefreshToken,
    setAuthCookie,
} = require("../config/LocalJWT");
const { blacklistToken } = require("../config/tokenBlacklist");

function getClientIp(req) {
    return req.headers["x-forwarded-for"]?.split(",").shift() || req.socket?.remoteAddress;
}

async function login(req, res) {
    try {
        const { email, password } = req.body;
        if (!email || !password) {
            return res.status(400).json({ success: false, message: "Email and password are required" });
        }

        const user = await User.findOne({ email });
        if (!user) return res.status(401).json({ success: false, message: "Invalid email or password" });

        const isMatch = await bcrypt.compare(password, user.password);
        if (!isMatch) return res.status(401).json({ success: false, message: "Invalid email or password" });

        const ip = getClientIp(req);

        if (user.twoFactorEnabled) {
            const tempToken = generateAccessToken(
                { id: user._id, email: user.email, requires2FA: true },
                "5m"
            );
            setAuthCookie(res, tempToken, 5 * 60 * 1000);

            return res.status(200).json({
                success: true,
                twoFactorEnabled: true,
                tempToken,
                message: "Please verify with 2FA",
            });
        }

        const accessToken = generateAccessToken({ id: user._id, email: user.email, dashboard: user.dashboard });
        const refreshToken = generateRefreshToken({ id: user._id });

        setAuthCookie(res, accessToken);

        user.lastLoginAt = new Date();
        user.lastLoginIP = ip;
        await user.save();

        return res.status(200).json({
            success: true,
            token: accessToken,
            refreshToken,
            user: {
                id: user._id,
                company: user.dashboard === "api" ? user.recordId : "N/A",
                email: user.email,
                name: user.name,
                dashboard: user.dashboard,
            },
        });
    } catch (error) {
        return res.status(500).json({ success: false, message: "Server error" });
    }
}

async function verifyLogin2FA(req, res) {
    try {
        const { email, verificationCode, tempToken } = req.body;
        if (!email || !verificationCode || !tempToken) {
            return res.status(400).json({ success: false, message: "Email, verification code, and token are required" });
        }

        const decoded = verifyAccessToken(tempToken);
        if (!decoded || decoded.email !== email || !decoded.requires2FA) {
            return res.status(401).json({ success: false, message: "Invalid or expired token. Please login again." });
        }

        const user = await User.findOne({ email });
        if (!user || !user.twoFactorSecret) {
            return res.status(401).json({ success: false, message: "User not found or 2FA not set up" });
        }

        const verified = speakeasy.totp.verify({
            secret: user.twoFactorSecret,
            encoding: "base32",
            token: verificationCode,
            window: 1,
        });

        if (!verified) {
            return res.status(401).json({ success: false, message: "Invalid verification code" });
        }

        const accessToken = generateAccessToken({ id: user._id, email: user.email, dashboard: user.dashboard });
        const refreshToken = generateRefreshToken({ id: user._id });

        setAuthCookie(res, accessToken);

        return res.status(200).json({
            success: true,
            accessToken,
            refreshToken,
            user: {
                id: user._id,
                email: user.email,
                name: user.name,
                dashboard: user.dashboard,
            },
        });
    } catch (error) {
        return res.status(500).json({ success: false, message: "Server error" });
    }
}

// 🔹 Refresh token endpoint
async function refreshToken(req, res) {
    try {
        const { refreshToken } = req.body;
        if (!refreshToken) {
            return res.status(400).json({ success: false, message: "Refresh token required" });
        }

        const payload = verifyRefreshToken(refreshToken);
        if (!payload) {
            return res.status(401).json({ success: false, message: "Invalid or expired refresh token" });
        }

        const newAccessToken = generateAccessToken({ id: payload.id });
        const newRefreshToken = generateRefreshToken({ id: payload.id }); // rotation

        setAuthCookie(res, newAccessToken);

        return res.json({ success: true, accessToken: newAccessToken, refreshToken: newRefreshToken });
    } catch (error) {
        return res.status(500).json({ success: false, message: "Server error" });
    }
}

// Logout function
function logout(req, res) {
    // Destroy session if it exists
    if (req.session) {
        req.session.destroy(() => {});
    }
    // Blacklist access and refresh tokens from cookies
    const accessToken = req.cookies?.token;
    const refreshToken = req.cookies?.refreshToken;
    if (accessToken) blacklistToken(accessToken);
    if (refreshToken) blacklistToken(refreshToken);
    // Clear auth cookies
    res.clearCookie('token');
    res.clearCookie('refreshToken');
    return res.status(200).json({ success: true, message: 'Logged out successfully' });
}

module.exports = {
    login,
    verifyLogin2FA,
    refreshToken,logout
};
