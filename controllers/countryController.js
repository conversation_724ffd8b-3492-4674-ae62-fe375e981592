const Country = require("../models/Countries");
const { createTask } = require("../config/EventHandler");
const User = require("../models/user");
const { getClientIp } = require("../helpers/requestHelper");

/* List active countries */
async function listCountries(req, res) {
  try {
    const countries = await Country.find({ deleted_at: null }).sort({ created_at: -1 });
    return res.status(200).json({ success: true, count: countries.length, data: countries });
  } catch (err) {
    return res.status(500).json({ success: false, message: "Failed to fetch countries", error: err.message });
  }
}

/* Create a country and enqueue a task */
async function createCountry(req, res) {
  try {
    const { country_name, country_code, currency_code, created_by } = req.body;
    const recordCount = await Country.countDocuments();
    const version = `${recordCount + 1}.0`;

    const newCountry = new Country({ country_name, country_code, currency_code, version, created_by });
    const savedCountry = await newCountry.save();

    try {
      const user = created_by ? await User.findById(created_by) : null;
      const ip = getClientIp(req);
      const taskData = {
        refId: savedCountry._id,
        type: "Country",
        title: `${user?.name || "Unknown"} requested a new Country "${country_name}"`,
        date: new Date(),
        user: created_by || null,
        ipAddress: ip,
      };
      await createTask(taskData);
    } catch (taskErr) {
      console.error("createTask failed:", taskErr?.message || taskErr);
    }

    return res.status(201).json({ success: true, data: savedCountry });
  } catch (err) {
    return res.status(500).json({ success: false, message: "Failed to create country", error: err.message });
  }
}

/* Soft delete a country */
async function deleteCountry(req, res) {
  try {
    const { id } = req.params;
    const updated = await Country.findByIdAndUpdate(id, { deleted_at: new Date() }, { new: true });
    if (!updated) return res.status(404).json({ success: false, message: "Country not found" });
    return res.status(200).json({ success: true, data: updated });
  } catch (err) {
    return res.status(500).json({ success: false, message: "Failed to delete country", error: err.message });
  }
}

/* Patch country active status */
async function patchStatus(req, res) {
  try {
    const { id } = req.params;
    const { is_active } = req.body;
    const updated = await Country.findByIdAndUpdate(id, { is_active }, { new: true });
    if (!updated) return res.status(404).json({ success: false, message: "Country not found" });
    return res.status(200).json({ success: true, message: "Country status updated", data: updated });
  } catch (err) {
    return res.status(500).json({ success: false, message: "Internal server error", error: err.message });
  }
}

/* Approve: set status active and bump version by 0.1 */
async function approve(req, res) {
  try {
    const { entityId } = req.body;
    if (!entityId) return res.status(400).json({ success: false, message: "record ID is required." });

    const record = await Country.findById(entityId);
    if (!record) return res.status(404).json({ success: false, message: "record not found." });

    const currentVersion = parseFloat(record.version) || 0.0;
    const updatedVersion = (currentVersion + 0.1).toFixed(1);

    await Country.findByIdAndUpdate(entityId, { status: "active", version: updatedVersion }, { new: true });
    return res.status(200).json({ success: true, message: "record approved successfully." });
  } catch (err) {
    return res.status(500).json({ success: false, message: "Internal server error.", error: err.message });
  }
}

/* Decline: set status decline, save reason, bump version by 0.1 */
async function decline(req, res) {
  try {
    const { entityId, reason } = req.body;
    if (!entityId || !reason) return res.status(400).json({ success: false, message: "record ID and reason are required." });

    const record = await Country.findById(entityId);
    if (!record) return res.status(404).json({ success: false, message: "record not found." });

    const currentVersion = parseFloat(record.version) || 0.0;
    const updatedVersion = (currentVersion + 0.1).toFixed(1);

    await Country.findByIdAndUpdate(entityId, { status: "decline", reason, version: updatedVersion }, { new: true });
    return res.status(200).json({ success: true, message: "record declined successfully." });
  } catch (err) {
    return res.status(500).json({ success: false, message: "Internal server error.", error: err.message });
  }
}

/* Modify: set status modify, save instructions, bump version by 0.2 */
async function modify(req, res) {
  try {
    const { entityId, instructions } = req.body;
    if (!entityId || !instructions) return res.status(400).json({ success: false, message: "record ID and modification instructions are required." });

    const record = await Country.findById(entityId);
    if (!record) return res.status(404).json({ success: false, message: "record not found." });

    const currentVersion = parseFloat(record.version) || 0.0;
    const updatedVersion = (currentVersion + 0.2).toFixed(1);

    await Country.findByIdAndUpdate(entityId, { status: "modify", reason: instructions, version: updatedVersion }, { new: true });
    return res.status(200).json({ success: true, message: "record modification request submitted successfully." });
  } catch (err) {
    return res.status(500).json({ success: false, message: "Internal server error.", error: err.message });
  }
}

module.exports = {
  listCountries,
  createCountry,
  deleteCountry,
  patchStatus,
  approve,
  decline,
  modify,
};