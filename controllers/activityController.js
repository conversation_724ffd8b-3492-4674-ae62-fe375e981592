const Activity = require("../models/Activity");

/**
 * Middlewares
 */
function validateApi<PERSON>ey(req, res, next) {
    // optional API key validation hook
    next();
}

function validateActivityData(req, res, next) {
    const { url, pathname, method } = req.body;
    if (!url || !pathname || !method) {
        return res.status(400).json({
            success: false,
            error: "Missing required fields: url, pathname, method",
            required: ["url", "pathname", "method"],
            optional: ["userAgent", "referer", "ip", "searchParams", "headers", "sessionId", "userId"],
        });
    }

    const validMethods = ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD"];
    if (!validMethods.includes(method.toUpperCase())) {
        return res.status(400).json({
            success: false,
            error: "Invalid HTTP method",
            validMethods,
        });
    }

    next();
}

/**
 * Controllers
 */
async function createActivity(req, res) {
    try {
        const activityData = {
            url: req.body.url,
            pathname: req.body.pathname,
            method: req.body.method.toUpperCase(),
            userAgent: req.body.userAgent || "Unknown",
            referer: req.body.referer || "",
            ip: req.body.ip || req.ip || "unknown",
            timestamp: req.body.timestamp ? new Date(req.body.timestamp) : new Date(),
            searchParams: req.body.searchParams || {},
            headers: {
                accept: req.body.headers?.accept || "",
                acceptLanguage: req.body.headers?.acceptLanguage || "",
            },
            sessionId: req.body.sessionId || null,
            userId: req.body.userId || null,
            responseTime: req.body.responseTime || null,
            statusCode: req.body.statusCode || 200,
        };

        const activity = new Activity(activityData);
        await activity.save();

        res.status(201).json({
            success: true,
            message: "Activity logged successfully",
            data: {
                id: activity._id,
                timestamp: activity.timestamp,
                pathname: activity.pathname,
                method: activity.method,
            },
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: "Failed to log activity",
            details: process.env.NODE_ENV === "development" ? error.message : undefined,
        });
    }
}

async function createBatchActivities(req, res) {
    try {
        const { activities } = req.body;
        if (!Array.isArray(activities) || activities.length === 0) {
            return res.status(400).json({ success: false, error: "Activities must be a non-empty array" });
        }
        if (activities.length > 100) {
            return res.status(400).json({ success: false, error: "Maximum 100 activities allowed per batch" });
        }

        const processed = activities.map((activity, index) => {
            if (!activity.url || !activity.pathname || !activity.method) {
                throw new Error(`Activity at index ${index} is missing required fields`);
            }
            return {
                url: activity.url,
                pathname: activity.pathname,
                method: activity.method.toUpperCase(),
                userAgent: activity.userAgent || "Unknown",
                referer: activity.referer || "",
                ip: activity.ip || req.ip || "unknown",
                timestamp: activity.timestamp ? new Date(activity.timestamp) : new Date(),
                searchParams: activity.searchParams || {},
                headers: {
                    accept: activity.headers?.accept || "",
                    acceptLanguage: activity.headers?.acceptLanguage || "",
                },
                sessionId: activity.sessionId || null,
                userId: activity.userId || null,
                responseTime: activity.responseTime || null,
                statusCode: activity.statusCode || 200,
            };
        });

        const saved = await Activity.insertMany(processed);

        res.status(201).json({
            success: true,
            message: `${saved.length} activities logged successfully`,
            data: { count: saved.length, ids: saved.map(a => a._id) },
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: "Failed to log batch activities",
            details: process.env.NODE_ENV === "development" ? error.message : undefined,
        });
    }
}

async function listActivities(req, res) {
    try {
        const {
            limit = 50,
            offset = 0,
            startDate,
            endDate,
            method,
            pathname,
            ip,
            userId,
            sessionId,
            sortBy = "timestamp",
            sortOrder = "desc",
        } = req.query;

        const filter = {};
        if (startDate || endDate) {
            filter.timestamp = {};
            if (startDate) filter.timestamp.$gte = new Date(startDate);
            if (endDate) filter.timestamp.$lte = new Date(endDate);
        }
        if (method) filter.method = method.toUpperCase();
        if (pathname) filter.pathname = new RegExp(pathname, "i");
        if (ip) filter.ip = ip;
        if (userId) filter.userId = userId;
        if (sessionId) filter.sessionId = sessionId;

        const sort = {};
        sort[sortBy] = sortOrder === "asc" ? 1 : -1;

        const activities = await Activity.find(filter)
            .sort(sort)
            .limit(Number.parseInt(limit))
            .skip(Number.parseInt(offset))
            .select("-__v");

        const total = await Activity.countDocuments(filter);

        res.json({
            success: true,
            data: {
                activities,
                pagination: {
                    total,
                    limit: Number.parseInt(limit),
                    offset: Number.parseInt(offset),
                    pages: Math.ceil(total / Number.parseInt(limit)),
                    currentPage: Math.floor(Number.parseInt(offset) / Number.parseInt(limit)) + 1,
                },
                filters: filter,
            },
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: "Failed to fetch activities",
            details: process.env.NODE_ENV === "development" ? error.message : undefined,
        });
    }
}

async function stats(req, res) {
    try {
        const { startDate, endDate, groupBy = "day" } = req.query;
        const start = startDate ? new Date(startDate) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        const end = endDate ? new Date(endDate) : new Date();

        const totalActivities = await Activity.countDocuments({ timestamp: { $gte: start, $lte: end } });
        const uniqueIPs = await Activity.distinct("ip", { timestamp: { $gte: start, $lte: end } });
        const uniquePages = await Activity.distinct("pathname", { timestamp: { $gte: start, $lte: end } });

        const methodStats = await Activity.aggregate([
            { $match: { timestamp: { $gte: start, $lte: end } } },
            { $group: { _id: "$method", count: { $sum: 1 } } },
            { $sort: { count: -1 } },
        ]);

        const deviceStats = await Activity.aggregate([
            { $match: { timestamp: { $gte: start, $lte: end } } },
            { $group: { _id: "$deviceType", count: { $sum: 1 } } },
            { $sort: { count: -1 } },
        ]);

        let timeGrouping = {};
        if (groupBy === "hour") timeGrouping = { $hour: "$timestamp" };
        else if (groupBy === "day") timeGrouping = { $dayOfYear: "$timestamp" };
        else if (groupBy === "month") timeGrouping = { $month: "$timestamp" };

        const timeStats = await Activity.aggregate([
            { $match: { timestamp: { $gte: start, $lte: end } } },
            { $group: { _id: timeGrouping, count: { $sum: 1 } } },
            { $sort: { _id: 1 } },
        ]);

        res.json({
            success: true,
            data: {
                summary: {
                    totalActivities,
                    uniqueIPs: uniqueIPs.length,
                    uniquePages: uniquePages.length,
                    period: { start, end },
                },
                methodDistribution: methodStats,
                deviceDistribution: deviceStats,
                timeDistribution: timeStats,
            },
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: "Failed to fetch activity statistics",
            details: process.env.NODE_ENV === "development" ? error.message : undefined,
        });
    }
}

async function popularPages(req, res) {
    try {
        const { limit = 10, startDate, endDate } = req.query;
        const start = startDate ? new Date(startDate) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        const end = endDate ? new Date(endDate) : new Date();

        const popularPagesResult = await Activity.aggregate([
            { $match: { timestamp: { $gte: start, $lte: end } } },
            {
                $group: {
                    _id: "$pathname",
                    visits: { $sum: 1 },
                    uniqueVisitors: { $addToSet: "$ip" },
                    lastVisit: { $max: "$timestamp" },
                },
            },
            {
                $project: {
                    pathname: "$_id",
                    visits: 1,
                    uniqueVisitors: { $size: "$uniqueVisitors" },
                    lastVisit: 1,
                    _id: 0,
                },
            },
            { $sort: { visits: -1 } },
            { $limit: Number.parseInt(limit) },
        ]);

        res.json({ success: true, data: popularPagesResult });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: "Failed to fetch popular pages",
            details: process.env.NODE_ENV === "development" ? error.message : undefined,
        });
    }
}

async function userActivities(req, res) {
    try {
        const { userId } = req.params;
        const { limit = 50, offset = 0 } = req.query;

        const activities = await Activity.find({ userId })
            .sort({ timestamp: -1 })
            .limit(Number.parseInt(limit))
            .skip(Number.parseInt(offset))
            .select("-__v");

        const total = await Activity.countDocuments({ userId });

        res.json({
            success: true,
            data: { userId, activities, total, limit: Number.parseInt(limit), offset: Number.parseInt(offset) },
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: "Failed to fetch user activities",
            details: process.env.NODE_ENV === "development" ? error.message : undefined,
        });
    }
}

async function cleanupActivities(req, res) {
    try {
        const { days = 30, dryRun = false } = req.query;
        const cutoffDate = new Date(Date.now() - Number.parseInt(days) * 24 * 60 * 60 * 1000);

        if (dryRun === "true") {
            const count = await Activity.countDocuments({ timestamp: { $lt: cutoffDate } });
            return res.json({
                success: true,
                message: `Would delete ${count} activities older than ${days} days`,
                dryRun: true,
                cutoffDate,
            });
        }

        const result = await Activity.deleteMany({ timestamp: { $lt: cutoffDate } });
        res.json({
            success: true,
            message: `Deleted ${result.deletedCount} activities older than ${days} days`,
            deletedCount: result.deletedCount,
            cutoffDate,
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: "Failed to cleanup activities",
            details: process.env.NODE_ENV === "development" ? error.message : undefined,
        });
    }
}

async function healthCheck(req, res) {
    try {
        await Activity.countDocuments().limit(1);
        res.json({
            success: true,
            status: "healthy",
            timestamp: new Date().toISOString(),
            database: "connected",
            version: "1.0.0",
        });
    } catch (error) {
        res.status(503).json({
            success: false,
            status: "unhealthy",
            timestamp: new Date().toISOString(),
            database: "disconnected",
            error: error.message,
        });
    }
}

module.exports = {
    validateApiKey,
    validateActivityData,
    createActivity,
    createBatchActivities,
    listActivities,
    stats,
    popularPages,
    userActivities,
    cleanupActivities,
    healthCheck,
};