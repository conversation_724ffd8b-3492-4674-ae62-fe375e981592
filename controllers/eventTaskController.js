const Task = require("../models/EventTask");
const {
  createTask: createTaskHandler,
  updateTask<PERSON>tatus,
  declineTask,
  modifyTask,
  getTaskById,
  getAllTasks,
  getRecordsByType,
} = require("../config/EventHandler");

/**
 * List tasks (uses EventHandler.getAllTasks if available)
 */
async function listTasks(req, res) {
  try {
    // Prefer the EventHandler helper if it exists
    if (typeof getAllTasks === "function") {
      const tasks = await getAllTasks(req.query);
      return res.status(200).json(tasks);
    }

    const tasks = await Task.find({ deleted_at: null }).populate("user").sort({ createdAt: -1 });
    return res.status(200).json(tasks);
  } catch (err) {
    return res.status(500).json({ error: err.message || "Failed to fetch tasks" });
  }
}

/**
 * Create a new task (delegates to EventHandler.createTask)
 */
async function createTask(req, res) {
  try {
    const payload = req.body || {};
    const task = await createT<PERSON>Hand<PERSON>(payload);
    return res.status(201).json(task);
  } catch (err) {
    return res.status(400).json({ error: err.message || "Failed to create task" });
  }
}

/**
 * Get single task by id
 */
async function getTask(req, res) {
  try {
    if (typeof getTaskById === "function") {
      const task = await getTaskById(req.params.id);
      return res.status(200).json(task);
    }
    const task = await Task.findById(req.params.id).populate("user");
    if (!task) return res.status(404).json({ error: "Task not found" });
    return res.status(200).json(task);
  } catch (err) {
    return res.status(404).json({ error: err.message || "Failed to fetch task" });
  }
}

/**
 * Mark task complete
 */
async function completeTask(req, res) {
  try {
    const actionPerformedBy = req.body.actionPerformedBy;
    const updated = await updateTaskStatus(req.params.id, "Done", actionPerformedBy);
    return res.status(200).json(updated);
  } catch (err) {
    return res.status(400).json({ error: err.message || "Failed to complete task" });
  }
}

/**
 * Decline task with reason
 */
async function declineTaskRoute(req, res) {
  try {
    const { reason, actionPerformedBy } = req.body;
    if (!reason || !reason.trim()) return res.status(400).json({ error: "Decline reason is required" });
    const updated = await declineTask(req.params.id, reason.trim(), actionPerformedBy);
    return res.status(200).json(updated);
  } catch (err) {
    return res.status(400).json({ error: err.message || "Failed to decline task" });
  }
}

/**
 * Request modification for a task with reason
 */
async function modifyTaskRoute(req, res) {
  try {
    const { reason, actionPerformedBy } = req.body;
    if (!reason || !reason.trim()) return res.status(400).json({ error: "Modification reason is required" });
    const updated = await modifyTask(req.params.id, reason.trim(), actionPerformedBy);
    return res.status(200).json(updated);
  } catch (err) {
    return res.status(400).json({ error: err.message || "Failed to request modification" });
  }
}

/**
 * Get records by type (delegates to EventHandler.getRecordsByType if present)
 */
async function recordsByType(req, res) {
  try {
    if (typeof getRecordsByType === "function") {
      const records = await getRecordsByType(req.params.type, req.query);
      return res.status(200).json(records);
    }
    return res.status(501).json({ error: "getRecordsByType not implemented in EventHandler" });
  } catch (err) {
    return res.status(500).json({ error: err.message || "Failed to fetch records by type" });
  }
}

module.exports = {
  listTasks,
  createTask,
  getTask,
  completeTask,
  declineTaskRoute,
  modifyTaskRoute,
  recordsByType,
};