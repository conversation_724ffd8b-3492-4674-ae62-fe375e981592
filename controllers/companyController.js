const Company = require('../models/company');
const CardProgram = require('../models/CardProgram');
const IndividualOnboarding = require('../models/IndividualOnboarding');
const { registerUser } = require("../controllers/userController");
const CompanyContact = require("../models/Contact");
const B2BAccount = require("../models/B2BAccount");
const Account = require("../models/Account");
const User = require('../models/user');

/* Helpers: generate unique RYVL id */
const generateRyvylId = () => {
  const randomNumber = Math.floor(******** + Math.random() * ********);
  return `RYVL-${randomNumber}`;
};

async function generateUniqueRyvylId() {
  let id;
  let exists = true;
  while (exists) {
    id = generateRyvylId();
    const existing = await Company.findOne({ ryvyl_id: id }).lean();
    if (!existing) exists = false;
  }
  return id;
}

/* Create company */
async function createCompany(req, res) {
  try {
    const ryvyl_id = await generateUniqueRyvylId();
    const companyData = { ...req.body, ryvyl_id, dashboardStatus: 'active' };

    const company = new Company(companyData);
    await company.save();

    // optional admin user creation if provided
    if (req.body.admin && req.body.admin.email && req.body.admin.name) {
      try {
        const admin = req.body.admin;
        const user = await registerUser(
          admin.name,
          admin.email,
          admin.roles || [],
          admin.status || 'active',
          admin.dashboard || 'company',
          admin.recordId || company._id,
          company._id
        );
        return res.status(201).json({ success: true, company, admin: user });
      } catch (uErr) {
        // return company even if user creation fails
        return res.status(201).json({ success: true, company, userError: uErr.message });
      }
    }

    return res.status(201).json({ success: true, data: company });
  } catch (err) {
    return res.status(400).json({ success: false, message: err.message });
  }
}

/* List companies with program and B2B attachments */
async function listCompanies(req, res) {
  try {
    const companies = await Company.find().sort({ created_at: -1 }).lean();
    const cardPrograms = await CardProgram.find().sort({ created_at: -1 }).lean();
    const b2bList = await B2BAccount.find().lean();

    const cardProgramIds = new Set(cardPrograms.map(p => String(p._id)));
    const uniqueCompanies = companies.filter(c => !cardProgramIds.has(String(c._id)));

    const companiesWithB2B = uniqueCompanies.map(company => {
      const b2bForCompany = b2bList.filter(b2b => String(b2b.parentCompany) === String(company._id));
      return { ...company, b2bList: b2bForCompany };
    });

    return res.status(200).json({ success: true, data: companiesWithB2B, programme: cardPrograms });
  } catch (err) {
    return res.status(500).json({ success: false, message: 'Failed to fetch company data', error: err.message });
  }
}

/* Get company by Mongo ID, ensure ryvyl_id exists */
async function getCompanyById(req, res) {
  try {
    const { id } = req.params;
    const company = await Company.findById(id);
    if (!company) return res.status(404).json({ message: 'Company not found' });

    if (!company.ryvyl_id) {
      company.ryvyl_id = await generateUniqueRyvylId();
      await company.save();
    }

    const contacts = await CompanyContact.find({ company: id }).sort({ created_at: -1 });
    const cardProgram = await CardProgram.find({ company: id }).sort({ created_at: -1 })
      .populate('company cardScheme programmeType binType binRangeId created_by programManagerType productVersionName');
    const companyAccount = await Account.find({ company: id });

    return res.json({ company, cip: cardProgram, contacts, companyAccount });
  } catch (error) {
    return res.status(500).json({ message: 'Error retrieving Company', error: error.message });
  }
}

/* Get company by ryvyl_id */
async function getCompanyByRyvylId(req, res) {
  try {
    const { ryvylID } = req.params;
    const company = await Company.findOne({ ryvyl_id: ryvylID });
    if (!company) return res.status(404).json({ message: 'Company not found' });

    const cardProgram = await CardProgram.find({ company: company._id }).sort({ created_at: -1 })
      .populate('company created_by programManagerType productVersionName');
    const companyAccount = await Account.find({ company: company._id });

    return res.json({ company, cip: cardProgram, companyAccount });
  } catch (error) {
    return res.status(500).json({ message: 'Error retrieving Company', error: error.message });
  }
}

/* Get banking clients (individuals + B2B) for company */
async function getBankingClients(req, res) {
  try {
    const { id } = req.params;
    const bankingCustomers = await IndividualOnboarding.find({ company: id })
      .populate('cardCurrency')
      .sort({ createdAt: -1 });
    const b2bList = await B2BAccount.find({ parentCompany: id });
    return res.json({ users: bankingCustomers, b2b: b2bList });
  } catch (error) {
    return res.status(500).json({ message: 'Error retrieving Company', error: error.message });
  }
}

/* Activate company (by recordId path param) */
async function activateCompany(req, res) {
  try {
    const { recordId } = req.params;
    if (!recordId) return res.status(400).json({ error: 'recordId is required' });

    const updated = await Company.findByIdAndUpdate(recordId, { dashboardStatus: 'ACTIVE' }, { new: true });
    if (!updated) return res.status(404).json({ error: 'Record not found' });
    return res.status(200).json({ message: 'Record activated successfully', data: updated });
  } catch (error) {
    return res.status(500).json({ error: 'An error occurred while activating the record', details: error.message });
  }
}

/* Disable / Enable company and user by email */
async function disableCompany(req, res) {
  try {
    const { id, email } = req.body;
    const companyData = await Company.findOneAndUpdate({ _id: id }, { status: 'disabled' }, { new: true });
    const user = await User.findOneAndUpdate({ email }, { status: 'disabled' }, { new: true });
    return res.status(200).json({ success: true, user, companyData });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Internal Server Error', error: error.message });
  }
}

async function enableCompany(req, res) {
  try {
    const { id, email } = req.body;
    const companyData = await Company.findOneAndUpdate({ _id: id }, { status: 'active' }, { new: true });
    const user = await User.findOneAndUpdate({ email }, { status: 'active' }, { new: true });
    return res.status(200).json({ success: true, user, companyData });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Internal Server Error', error: error.message });
  }
}

/* Update company (partial replace of provided fields) */
async function updateCompany(req, res) {
  try {
    const { id } = req.params;
    const updateData = req.body;
    const company = await Company.findById(id);
    if (!company) return res.status(404).json({ success: false, message: 'Company not found' });

    Object.keys(updateData).forEach(key => company[key] = updateData[key]);
    await company.save();
    return res.status(200).json({ success: true, message: 'Company updated successfully', data: company });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Failed to update company', error: error.message });
  }
}

/* Update alert settings */
async function updateAlertSettings(req, res) {
  try {
    const { id } = req.params;
    const updateData = req.body;
    const updatedCompany = await Company.findByIdAndUpdate(id, updateData, { new: true, runValidators: true });
    if (!updatedCompany) return res.status(404).json({ success: false, message: 'Company not found' });
    return res.status(200).json({ success: true, message: 'Company updated successfully', data: updatedCompany });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Server error', error: error.message });
  }
}

/* Create or update a user for a company (moved from route) */
async function createUserForCompany(req, res) {
  try {
    const { id } = req.params;
    const { name, email, roles, status, dashboard, recordId, company: companyId, permissions = [], permissionAudit = [] } = req.body;

    const currentCompany = await Company.findById(id);
    if (!currentCompany) return res.status(404).json({ success: false, message: 'Company not found' });

    const oldPermissions = currentCompany.permissions || [];
    const addedPermissions = permissions.filter(p => !oldPermissions.includes(p));
    const removedPermissions = oldPermissions.filter(p => !permissions.includes(p));
    const changeDetails = [];
    if (addedPermissions.length) changeDetails.push(`Added: ${addedPermissions.join(", ")}`);
    if (removedPermissions.length) changeDetails.push(`Removed: ${removedPermissions.join(", ")}`);
    if (!changeDetails.length) changeDetails.push("No changes in permissions");

    currentCompany.permissions = permissions;
    currentCompany.permissionAudit = permissionAudit;

    const existingUser = await User.findOne({ email });
    if (existingUser) {
      currentCompany.permissionsLog = currentCompany.permissionsLog || [];
      currentCompany.permissionsLog.push({
        action: "Permission Update",
        details: changeDetails.join(" | "),
        username: name,
        email,
        userId: existingUser._id
      });
      await currentCompany.save();
      return res.status(200).json({ success: true, message: 'Permissions updated, but user email already exists', user: existingUser });
    }

    const newUser = await registerUser(name, email, roles || [], status || 'active', dashboard || 'company', recordId || null, companyId || id);
    currentCompany.permissionsLog = currentCompany.permissionsLog || [];
    currentCompany.permissionsLog.push({
      action: "User Created",
      details: changeDetails.join(" | "),
      username: name,
      email,
      userId: newUser._id
    });
    await currentCompany.save();

    return res.status(201).json({ success: true, message: 'User registered successfully', user: newUser });
  } catch (error) {
    return res.status(400).json({ success: false, message: error.message || 'An error occurred' });
  }
}

module.exports = {
  createCompany,
  listCompanies,
  getCompanyById,
  getCompanyByRyvylId,
  getBankingClients,
  activateCompany,
  disableCompany,
  enableCompany,
  updateCompany,
  updateAlertSettings,
  createUserForCompany,
};