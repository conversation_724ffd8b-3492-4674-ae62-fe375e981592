const fs = require('fs');
const path = require('path');
const CardImage = require('../models/CardImages');

const UPLOAD_DIR = path.join(__dirname, '..', 'uploads');
if (!fs.existsSync(UPLOAD_DIR)) fs.mkdirSync(UPLOAD_DIR, { recursive: true });

async function uploadImages(req, res) {
  try {
    const files = req.files || {};
    const front = files.front_side?.[0] || null;
    const back = files.back_side?.[0] || null;

    if (!front && !back) {
      return res.status(400).json({ success: false, message: 'No files uploaded' });
    }

    const doc = new CardImage({
      front: front ? {
        filename: front.filename,
        originalName: front.originalname,
        path: path.relative(path.join(__dirname, '..'), front.path).replace(/\\/g, '/'),
        mimeType: front.mimetype,
        size: front.size
      } : null,
      back: back ? {
        filename: back.filename,
        originalName: back.originalname,
        path: path.relative(path.join(__dirname, '..'), back.path).replace(/\\/g, '/'),
        mimeType: back.mimetype,
        size: back.size
      } : null,
      meta: req.body.meta || {},
      createdBy: req.user?.id || req.body.createdBy || null,
      createdAt: new Date()
    });

    await doc.save();
    return res.status(201).json({ success: true, data: doc });
  } catch (err) {
    return res.status(500).json({ success: false, message: err.message });
  }
}

async function listImages(req, res) {
  try {
    const { limit = 100, skip = 0 } = req.query;
    const items = await CardImage.find().populate('company').populate('product_version')
      .sort({ createdAt: -1 })
      .skip(Number(skip))
      .limit(Math.min(Number(limit), 500));
    return res.json({ success: true, count: items.length, data: items });
  } catch (err) {
    return res.status(500).json({ success: false, message: err.message });
  }
}

async function deleteImage(req, res) {
  try {
    const id = req.params.id;
    const record = await CardImage.findById(id);
    if (!record) return res.status(404).json({ success: false, message: 'Record not found' });

    const toUnlink = [];
    if (record.front?.filename) toUnlink.push(path.join(UPLOAD_DIR, record.front.filename));
    if (record.back?.filename) toUnlink.push(path.join(UPLOAD_DIR, record.back.filename));

    for (const p of toUnlink) {
      try { if (fs.existsSync(p)) fs.unlinkSync(p); } catch (e) { /* ignore unlink errors */ }
    }

    await record.remove();
    return res.json({ success: true, message: 'Record deleted' });
  } catch (err) {
    return res.status(500).json({ success: false, message: err.message });
  }
}

module.exports = {
  uploadImages,
  listImages,
  deleteImage
};