const fs = require('fs');
const path = require('path');
const CardScheme = require('../models/CardScheme');
const { createTask, updateTaskByRecordId } = require('../config/EventHandler');
const User = require('../models/user');

const UPLOAD_DIR = path.join(__dirname, '..', 'uploads');
if (!fs.existsSync(UPLOAD_DIR)) fs.mkdirSync(UPLOAD_DIR, { recursive: true });

// Create a new card scheme
exports.createCardScheme = async (req, res) => {
  try {
    const payload = req.body || {};
    if (req.file) {
      payload.scheme_logo = req.file.path;
    }
    payload.version = payload.version ?? '0.0';
    payload.status = payload.status ?? 'pending';

    const scheme = new CardScheme(payload);
    const saved = await scheme.save();

    const user = await User.findById(payload.created_by);
    const ip =
      req.headers["x-forwarded-for"]?.split(",").shift() ||
      req.socket?.remoteAddress;
    const taskData = {
      refId: saved._id,
      type: 'Card Scheme',
      title: user.name + ' requested a new card scheme "' + payload.scheme_name + '"',
      date: new Date(),
      user: payload.created_by,
      ipAddress: ip,
    };

    await createTask(taskData)

    return res.status(201).json({ success: true, data: saved });
  } catch (err) {
    return res.status(400).json({ success: false, message: err.message });
  }
};

// Fetch all card schemes
exports.getCardSchemes = async (req, res) => {
  try {
    const { page = 1, limit = 100 } = req.query;
    const skip = (Math.max(Number(page), 1) - 1) * Number(limit);

    const [items, total] = await Promise.all([
      CardScheme.find({ deleted_at: null }).sort({ created_at: -1 }).skip(skip).limit(Number(limit)),
      CardScheme.countDocuments({ deleted_at: null }),
    ]);

    return res.json({ success: true, count: items.length, total, data: items });
  } catch (err) {
    return res.status(500).json({ success: false, message: err.message });
  }
};

exports.deleteCardScheme = async (req, res) => {
  try {
    const { id } = req.params;
    const updated = await CardScheme.findByIdAndUpdate(id, { deleted_at: new Date() }, { new: true });
    if (!updated) return res.status(404).json({ success: false, message: 'Record not found' });
    return res.json({ success: true, message: 'Scheme deleted (soft) successfully' });
  } catch (err) {
    return res.status(500).json({ success: false, message: err.message });
  }
};

exports.approveScheme = async (req, res) => {
  try {
    const { schemeId } = req.body;
    if (!schemeId) return res.status(400).json({ message: 'Scheme ID is required.' });

    const scheme = await CardScheme.findById(schemeId);
    if (!scheme) return res.status(404).json({ message: 'Scheme not found.' });

    const currentVersion = parseFloat(scheme.version) || 0.0;
    const updatedVersion = (currentVersion + 0.1).toFixed(1);

    await CardScheme.findByIdAndUpdate(schemeId, { status: 'active', version: updatedVersion }, { new: true });

    return res.status(200).json({ success: true, message: 'Scheme approved successfully.' });
  } catch (err) {
    return res.status(500).json({ success: false, message: err.message });
  }
};

exports.declineScheme = async (req, res) => {
  try {
    const { schemeId, reason } = req.body;
    if (!schemeId || !reason) return res.status(400).json({ message: 'Scheme ID and reason are required.' });

    const scheme = await CardScheme.findById(schemeId);
    if (!scheme) return res.status(404).json({ message: 'Scheme not found.' });

    const currentVersion = parseFloat(scheme.version) || 0.0;
    const updatedVersion = (currentVersion + 0.1).toFixed(1);

    await CardScheme.findByIdAndUpdate(schemeId, { status: 'decline', reason, version: updatedVersion }, { new: true });

    return res.status(200).json({ success: true, message: 'Scheme declined successfully.' });
  } catch (err) {
    return res.status(500).json({ success: false, message: err.message });
  }
};

exports.modifyScheme = async (req, res) => {
  try {
    const { schemeId, instructions } = req.body;
    if (!schemeId || !instructions) return res.status(400).json({ message: 'Scheme ID and modification instructions are required.' });

    const scheme = await CardScheme.findById(schemeId);
    if (!scheme) return res.status(404).json({ message: 'Scheme not found.' });

    const currentVersion = parseFloat(scheme.version) || 0.0;
    const updatedVersion = (currentVersion + 0.2).toFixed(1);

    await CardScheme.findByIdAndUpdate(
      schemeId,
      { status: 'modify', reason: instructions, version: updatedVersion },
      { new: true }
    );

    return res.status(200).json({ success: true, message: 'Scheme modification request submitted successfully.' });
  } catch (err) {
    return res.status(500).json({ success: false, message: err.message });
  }
};

exports.updateCardScheme = async (req, res) => {
  try {
    const { id } = req.params;
    const { scheme_name, scheme_description, scheme_type } = req.body;

    let scheme = await CardScheme.findById(id);
    if (!scheme) return res.status(404).json({ message: 'Card scheme not found.' });

    scheme.scheme_name = scheme_name ?? scheme.scheme_name;
    scheme.scheme_description = scheme_description ?? scheme.scheme_description;
    scheme.scheme_type = scheme_type ?? scheme.scheme_type;

    if (req.file) {
      scheme.scheme_logo = req.file.path;
    }

    const currentVersion = parseFloat(scheme.version) || 0.0;
    scheme.version = (currentVersion + 0.1).toFixed(1);
    scheme.status = 'pending';

    const updated = await scheme.save();
    try { await updateTaskByRecordId(updated._id, updated.scheme_name); } catch (e) { /* non-fatal */ }

    return res.status(200).json({ success: true, data: updated });
  } catch (err) {
    return res.status(500).json({ success: false, message: err.message });
  }
};
