const CardBin = require('../models/CardBin');

/**
 * Create a new CardBin
 */
exports.createCardBin = async (req, res) => {
  try {
    const payload = req.body;
    const cardBin = new CardBin(payload);
    const saved = await cardBin.save();
    return res.status(201).json({ success: true, data: saved });
  } catch (err) {
    return res.status(400).json({ success: false, message: err.message });
  }
}

/**
 * Get list of CardBins with optional filters and pagination
 */
exports.getCardBins = async (req, res) => {
  try {
    const { company, productVersion, page = 1, limit = 50 } = req.query;
    const filter = {};
    if (company) filter.company = company;
    if (productVersion) filter.product_version = productVersion;

    const skip = (Math.max(Number(page), 1) - 1) * Number(limit);
    const [items, total] = await Promise.all([
      CardBin.find(filter).skip(skip).limit(Number(limit)).sort({ createdAt: -1 }),
      CardBin.countDocuments(filter),
    ]);

    return res.json({
      success: true,
      data: items,
      meta: { total, page: Number(page), limit: Number(limit) },
    });
  } catch (err) {
    return res.status(500).json({ success: false, message: err.message });
  }
}
