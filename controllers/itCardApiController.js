const { sendGetRequest, sendPostRequest, sendPutRequest, sendPatchRequest } = require("../config/ApiInstense");
const { getDecryptedCardNumber } = require("../utils/cardUtils");
const Card = require("../models/AccountCard");
const IndividualOnboarding = require('../models/IndividualOnboarding');
const crypto = require('crypto');
const bcrypt = require('bcryptjs');

const IT_CARD_API_URL = process.env.IT_CARD_API_URL;
if (!IT_CARD_API_URL) {
  throw new Error('IT_CARD_API_URL environment variable is not defined');
}

async function listCards(req, res) {
  try {
    const cards = await Card.find();
    return res.status(200).json({ success: true, cards });
  } catch (error) {
    return res.status(500).json({ success: false, message: "Error fetching cards", error: error.message });
  }
}

async function proxyPostToItCard(req, res, pathBuilder) {
  try {
    const url = `${IT_CARD_API_URL}${pathBuilder(req)}`;
    const body = req.body && Object.keys(req.body).length ? req.body : '';
    const apiResponse = await sendPostRequest(url, typeof body === 'string' ? body : JSON.stringify(body));
    return res.status(200).json({ success: true, card: apiResponse });
  } catch (error) {
    return res.status(500).json({ success: false, message: "Error while calling api", error: error.message });
  }
}

async function proxyGetToItCard(req, res, pathBuilder) {
  try {
    const url = `${IT_CARD_API_URL}${pathBuilder(req)}`;
    const apiResponse = await sendGetRequest(url);
    return res.status(200).json({ success: true, card: apiResponse });
  } catch (error) {
    return res.status(500).json({ success: false, message: "Error while calling api", error: error.message });
  }
}

async function lockCard(req, res) {
  return proxyPostToItCard(req, res, (r) => `/cards/${r.params.id}/lock`);
}
async function unlockCard(req, res) {
  return proxyPostToItCard(req, res, (r) => `/cards/${r.params.id}/unlock`);
}
async function getStatus(req, res) {
  return proxyGetToItCard(req, res, (r) => `/cards/${r.params.id}/status`);
}
async function getLimits(req, res) {
  return proxyGetToItCard(req, res, (r) => `/cards/${r.params.id}/limits`);
}
async function patchCardLimits(req, res) {
  try {
    const url = `${IT_CARD_API_URL}/cards/${req.params.id}/limits`;
    const apiResponse = await sendPatchRequest(url, req.body);
    return res.status(200).json({ success: true, card: apiResponse });
  } catch (error) {
    return res.status(500).json({ success: false, message: "Error while calling api", error: error.message });
  }
}
async function closeCard(req, res) { return proxyPostToItCard(req, res, (r) => `/cards/${r.params.id}/restrictClosed`); }
async function restrict(req, res) { return proxyPostToItCard(req, res, (r) => `/cards/${r.params.id}/restrict`); }
async function restrictFraud(req, res) { return proxyPostToItCard(req, res, (r) => `/cards/${r.params.id}/restrictFraud`); }
async function restrictStolen(req, res) { return proxyPostToItCard(req, res, (r) => `/cards/${r.params.id}/restrictStolen`); }
async function restrictLost(req, res) { return proxyPostToItCard(req, res, (r) => `/cards/${r.params.id}/restrictLost`); }
async function forcePinLock(req, res) { return proxyPostToItCard(req, res, (r) => `/cards/${r.params.id}/forcePinLock`); }
async function restrictMobileLost(req, res) { return proxyPostToItCard(req, res, (r) => `/cards/${r.params.id}/restrictMobileLost`); }

async function resetPinTries(req, res) {
  try {
    const url = `${IT_CARD_API_URL}/cards/${req.params.id}/resetPinTries`;
    const apiResponse = await sendPostRequest(url, '');
    return res.status(200).json({ success: true, card: apiResponse });
  } catch (error) {
    return res.status(500).json({ success: false, message: "Error while calling api", error: error.message });
  }
}

async function resignCard(req, res) { return proxyPostToItCard(req, res, (r) => `/cards/${r.params.id}/resignCard`); }
async function cancelResign(req, res) { return proxyPostToItCard(req, res, (r) => `/cards/${r.params.id}/cancelResignCard`); }
async function replaceCard(req, res) { return proxyPostToItCard(req, res, (r) => `/cards/${r.params.id}/replaceCard`); }

async function activateCard(req, res) {
  try {
    const { id } = req.params;
    const url = `${IT_CARD_API_URL}/cards/${id}/activate`;
    const request = JSON.stringify(req.body || {});
    const apiResponse = await sendPostRequest(url, request);

    const card = await Card.findOne({ cardKey: id });
    if (card) {
      card.status = 'ACTIVE';
      await card.save();
    }

    return res.status(200).json({ success: true, card: apiResponse });
  } catch (error) {
    return res.status(500).json({ success: false, message: "Error while calling api", error: error.message });
  }
}

async function changeName(req, res) {
  try {
    const { id } = req.params;
    const { embossName1, embossName2 } = req.body;
    const url = `${IT_CARD_API_URL}/cards/${id}/embossName`;
    const request = JSON.stringify({ embossName1, embossName2 });
    const apiResponse = await sendPostRequest(url, request);
    return res.status(200).json({ success: true, card: apiResponse });
  } catch (error) {
    return res.status(500).json({ success: false, message: "Error while calling api", error: error.message });
  }
}

async function associatedClients(req, res) {
  return proxyGetToItCard(req, res, (r) => `/cards/${r.params.id}/associatedClients`);
}

async function embossInfo(req, res) {
  try {
    const url = `${IT_CARD_API_URL}/cards/${req.params.id}/embossInfo`;
    const apiResponse = await sendPostRequest(url, '');
    return res.status(200).json({ success: true, card: apiResponse });
  } catch (error) {
    return res.status(500).json({ success: false, message: "Error while calling api", error: error.message });
  }
}

async function actionLog(req, res) {
  try {
    const { id } = req.params;
    const today = new Date();
    const endDate = today.toISOString().split("T")[0];
    const last30Days = new Date(today);
    last30Days.setDate(today.getDate() - 30);
    const startDate = last30Days.toISOString().split("T")[0];
    const url = `${IT_CARD_API_URL}/${id}/actionLog/?start_date=${startDate}&end_date=${endDate}&language=EN`;
    const apiResponse = await sendGetRequest(url);
    return res.status(200).json({ success: true, actionLog: apiResponse });
  } catch (error) {
    return res.status(500).json({ success: false, message: "Error fetching action log", error: error.message });
  }
}

async function transactions(req, res) {
  try {
    const { id } = req.params;
    const today = new Date();
    const endDate = today.toISOString().split('T')[0];
    const last30Days = new Date(today);
    last30Days.setDate(today.getDate() - 30);
    const startDate = last30Days.toISOString().split('T')[0];
    const url = `${IT_CARD_API_URL}/${id}/transactions?id_type=CARD_KEY&start_date=${startDate}&end_date=${endDate}&start_amount=0&end_amount=10000&successful=false&current_account=false&page=1&per_page=10`;
    const apiResponse = await sendGetRequest(url);
    return res.status(200).json({ success: true, transactions: apiResponse });
  } catch (error) {
    return res.status(500).json({ success: false, message: "Error fetching transactions", error: error.message });
  }
}

async function cardNumber(req, res) {
  try {
    const { id } = req.params;
    const response = await getDecryptedCardNumber(id);
    if (response.success) return res.status(200).json(response);
    return res.status(500).json({ success: false, message: "Error fetching card number", error: response.message });
  } catch (error) {
    return res.status(500).json({ success: false, message: "Error fetching card number", error: error.message });
  }
}

async function change3dAns(req, res) {
  try {
    const { id } = req.params;
    const { newAnswer } = req.body;
    const newPassword = await bcrypt.hash(newAnswer, 10);
    await Card.findOneAndUpdate({ cardKey: id }, { set_pin: true, current_password: newPassword });
    return res.status(200).json({ success: true });
  } catch (error) {
    return res.status(500).json({ success: false, message: "Error while calling api", error: error.message });
  }
}

async function change3dPhone(req, res) {
  try {
    const { id } = req.params;
    const { newPhoneNumber } = req.body;
    const c_card = await Card.findOneAndUpdate({ cardKey: id }, { authPhoneNumber: newPhoneNumber }, { new: true });
    if (c_card?.onboarding) {
      await IndividualOnboarding.findByIdAndUpdate(c_card.onboarding, { $set: { "personalInfo.authPhoneNumber": newPhoneNumber } });
    }
    return res.status(200).json({ success: true });
  } catch (error) {
    return res.status(500).json({ success: false, message: "Error while calling api", error: error.message });
  }
}

async function changeNickname(req, res) {
  try {
    const { id } = req.params;
    const { nickname } = req.body;
    await Card.findOneAndUpdate({ cardKey: id }, { nickName: nickname });
    return res.status(200).json({ success: true });
  } catch (error) {
    return res.status(500).json({ success: false, message: "Error while calling api", error: error.message });
  }
}

async function setLimit(req, res) {
  try {
    const { id } = req.params;
    const { limitType, limitValue } = req.body;
    const url = `${IT_CARD_API_URL}/cards/${id}/limits/${limitType}`;
    const request = JSON.stringify({ value: limitValue });
    const apiResponse = await sendPutRequest(url, request);
    return res.status(200).json({ success: true, card: apiResponse });
  } catch (error) {
    return res.status(500).json({ success: false, message: "Error while calling api", error: error.message });
  }
}

function encryptPin(pin) {
  const hash = crypto.createHash('sha256').update(pin).digest();
  return hash.toString('base64');
}

module.exports = {
  listCards,
  lockCard,
  unlockCard,
  getStatus,
  getLimits,
  patchCardLimits,
  closeCard,
  restrict,
  restrictFraud,
  restrictStolen,
  restrictLost,
  forcePinLock,
  restrictMobileLost,
  resetPinTries,
  resignCard,
  cancelResign,
  replaceCard,
  activateCard,
  changeName,
  associatedClients,
  embossInfo,
  actionLog,
  transactions,
  cardNumber,
  change3dAns,
  change3dPhone,
  changeNickname,
  setLimit,
  encryptPin,
};