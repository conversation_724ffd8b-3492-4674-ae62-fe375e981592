const BinVariant = require('../models/BinVariant');
const { createTask, updateTaskByRecordId } = require('../config/EventHandler');
const User = require('../models/user');

/* Helpers */
function getClientIp(req) {
    return req.headers['x-forwarded-for']?.split(',').shift() || req.socket?.remoteAddress;
}

/* Create */
async function createBinVariant(req, res) {
    try {
        const bin = new BinVariant(req.body);
        const saved = await bin.save();

        try {
            const user = await User.findById(req.body.created_by);
            const ip = getClientIp(req);
            const taskData = {
                refId: saved._id,
                type: 'BIN Variant',
                title: (user?.name || 'Unknown') + ' requested a new BIN Variant "' + (req.body.variant || '') + '"',
                date: new Date(),
                user: user?._id || null,
                ipAddress: ip,
            };
            await createTask(taskData);
        } catch (taskErr) {
            console.error('createTask failed:', taskErr?.message || taskErr);
        }

        return res.status(201).json({ success: true, data: saved });
    } catch (err) {
        return res.status(400).json({ success: false, message: err.message });
    }
}

/* List */
async function listBinVariants(req, res) {
    try {
        const items = await BinVariant.find({ deleted_at: null }).populate('created_by').sort({ created_at: -1 });
        return res.status(200).json({ success: true, count: items.length, data: items });
    } catch (err) {
        return res.status(400).json({ success: false, message: err.message });
    }
}

/* Soft delete */
async function deleteBinVariant(req, res) {
    try {
        const { id } = req.params;
        const updated = await BinVariant.findByIdAndUpdate(id, { deleted_at: new Date() }, { new: true });
        if (!updated) return res.status(404).json({ success: false, message: 'Record not found' });
        return res.json({ success: true, message: 'Record deleted successfully' });
    } catch (err) {
        return res.status(500).json({ success: false, message: 'Error deleting Record', error: err.message });
    }
}

/* Approve */
async function approveBinVariant(req, res) {
    try {
        const { entityId } = req.body;
        if (!entityId) return res.status(400).json({ message: 'record ID is required.' });

        const record = await BinVariant.findById(entityId);
        if (!record) return res.status(404).json({ message: 'record not found.' });

        const currentVersion = parseFloat(record.version) || 0.0;
        const updatedVersion = (currentVersion + 0.1).toFixed(1);

        await BinVariant.findByIdAndUpdate(entityId, { status: 'active', version: updatedVersion }, { new: true });
        return res.status(200).json({ message: 'record approved successfully.' });
    } catch (error) {
        return res.status(500).json({ message: 'Internal server error.' });
    }
}

/* Decline */
async function declineBinVariant(req, res) {
    try {
        const { entityId, reason } = req.body;
        if (!entityId || !reason) return res.status(400).json({ message: 'record ID and reason are required.' });

        const record = await BinVariant.findById(entityId);
        if (!record) return res.status(404).json({ message: 'record not found.' });

        const currentVersion = parseFloat(record.version) || 0.0;
        const updatedVersion = (currentVersion + 0.1).toFixed(1);

        await BinVariant.findByIdAndUpdate(entityId, { status: 'decline', reason, version: updatedVersion }, { new: true });
        return res.status(200).json({ message: 'record declined successfully.' });
    } catch (error) {
        return res.status(500).json({ message: 'Internal server error.' });
    }
}

/* Modify (request changes) */
async function modifyBinVariant(req, res) {
    try {
        const { entityId, instructions } = req.body;
        if (!entityId || !instructions) return res.status(400).json({ message: 'record ID and modification instructions are required.' });

        const record = await BinVariant.findById(entityId);
        if (!record) return res.status(404).json({ message: 'record not found.' });

        const currentVersion = parseFloat(record.version) || 0.0;
        const updatedVersion = (currentVersion + 0.2).toFixed(1);

        await BinVariant.findByIdAndUpdate(entityId, { status: 'modify', reason: instructions, version: updatedVersion }, { new: true });
        return res.status(200).json({ message: 'record modification request submitted successfully.' });
    } catch (error) {
        return res.status(500).json({ message: 'Internal server error.' });
    }
}

/* Update (PUT /:id) */
async function updateBinVariant(req, res) {
    try {
        const { id } = req.params;
        const updateData = req.body;
        const actionPerformedBy = req.body.updated_by;

        let record = await BinVariant.findById(id);
        if (!record) return res.status(404).json({ message: 'BIN variant not found.' });

        if (updateData.variant) record.variant = updateData.variant;
        if (updateData.bin_suffix) record.bin_suffix = updateData.bin_suffix;

        const currentVersion = parseFloat(record.version) || 0.0;
        record.version = (currentVersion + 0.1).toFixed(1);
        record.status = 'pending';
        record.updated_by = actionPerformedBy;
        record.updated_at = new Date();

        const updatedRecord = await record.save();

        try {
            await updateTaskByRecordId(id, updatedRecord.variant);
        } catch (taskErr) {
            console.error('updateTaskByRecordId failed:', taskErr?.message || taskErr);
        }

        return res.status(200).json({ message: 'BIN variant updated successfully.', data: updatedRecord });
    } catch (error) {
        return res.status(500).json({ message: 'Internal server error.', error: error.message });
    }
}

module.exports = {
    createBinVariant,
    listBinVariants,
    deleteBinVariant,
    approveBinVariant,
    declineBinVariant,
    modifyBinVariant,
    updateBinVariant,
};