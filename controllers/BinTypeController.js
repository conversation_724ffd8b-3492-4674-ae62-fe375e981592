const mongoose = require("mongoose");
const BinType = require("../models/BinType");
const User = require("../models/user");
const { createTask } = require("../config/EventHandler");

const { isValidObjectId } = mongoose;

function validateObjectId(req, res, next) {
    if (!isValidObjectId(req.params.id)) {
        return res.status(400).json({ success: false, message: "Invalid ID format" });
    }
    next();
}

async function listBinTypes(req, res) {
    try {
        const { type, status, programmeType, binVariant, binCategory } = req.query;
        const filter = { deleted_at: null };

        if (type) filter.type = type;
        if (status) filter.status = status;
        if (programmeType && isValidObjectId(programmeType)) filter.programmeType = programmeType;
        if (binVariant && isValidObjectId(binVariant)) filter.binVariant = binVariant;
        if (binCategory && isValidObjectId(binCategory)) filter.binCategory = binCategory;

        const binTypes = await BinType.find(filter)
            .populate("programmeType")
            .populate("binVariant")
            .populate({
                path: "binCategory",
                populate: { path: "currency" }
            })
            .populate("company", "company_name")
            .populate("created_by", "name email")
            .sort({ created_at: -1 });

        return res.status(200).json({ success: true, count: binTypes.length, data: binTypes });
    } catch (error) {
        return res.status(500).json({ success: false, message: "Server Error", error: error.message });
    }
}

async function getBinTypeById(req, res) {
    try {
        const binType = await BinType.findOne({ _id: req.params.id, deleted_at: null })
            .populate("programmeType")
            .populate("binVariant")
            .populate("binCategory")
            .populate("created_by", "name email");

        if (!binType) return res.status(404).json({ success: false, message: "Bin type not found" });

        return res.status(200).json({ success: true, data: binType });
    } catch (error) {
        return res.status(500).json({ success: false, message: "Server Error", error: error.message });
    }
}

async function createBinType(req, res) {
    try {
        const { type, programmeType, binVariant, binCategory, status, version, reason, company, created_by } = req.body;

        if (!type || !programmeType || !binVariant || !binCategory) {
            return res.status(400).json({ success: false, message: "Please provide all required fields" });
        }

        if (!isValidObjectId(programmeType) || !isValidObjectId(binVariant) || !isValidObjectId(binCategory)) {
            return res.status(400).json({ success: false, message: "Invalid ID format for reference fields" });
        }

        if (!created_by || !isValidObjectId(created_by)) {
            return res.status(400).json({ success: false, message: "Valid created_by user ID is required" });
        }

        const newBinType = new BinType({
            type,
            programmeType,
            binVariant,
            binCategory,
            status: status || "pending",
            version,
            reason,
            company,
            created_by,
        });

        const savedBinType = await newBinType.save();

        // create task (best-effort, don't fail request)
        try {
            const user = await User.findById(created_by);
            const ip = req.headers["x-forwarded-for"]?.split(",").shift() || req.socket?.remoteAddress;
            const taskData = {
                refId: savedBinType._id,
                type: 'Bin Type',
                title: (user?.name || 'Unknown') + ' requested a new Bin Type "' + savedBinType.type + '"',
                date: new Date(),
                user: user?._id || null,
                ipAddress: ip,
            };
            await createTask(taskData);
        } catch (taskErr) {
            console.error('createTask failed:', taskErr?.message || taskErr);
        }

        return res.status(201).json({ success: true, data: savedBinType });
    } catch (error) {
        return res.status(500).json({ success: false, message: "Server Error", error: error.message });
    }
}

async function updateBinType(req, res) {
    try {
        const { type, programmeType, binVariant, binCategory, status, version, reason } = req.body;

        const binType = await BinType.findOne({ _id: req.params.id, deleted_at: null });
        if (!binType) return res.status(404).json({ success: false, message: "Bin type not found" });

        if (programmeType && !isValidObjectId(programmeType)) {
            return res.status(400).json({ success: false, message: "Invalid programmeType ID format" });
        }
        if (binVariant && !isValidObjectId(binVariant)) {
            return res.status(400).json({ success: false, message: "Invalid binVariant ID format" });
        }
        if (binCategory && !isValidObjectId(binCategory)) {
            return res.status(400).json({ success: false, message: "Invalid binCategory ID format" });
        }

        if (type) binType.type = type;
        if (programmeType) binType.programmeType = programmeType;
        if (binVariant) binType.binVariant = binVariant;
        if (binCategory) binType.binCategory = binCategory;
        if (status) binType.status = status;
        if (version) binType.version = version;
        if (reason) binType.reason = reason;

        const updatedBinType = await binType.save();
        return res.status(200).json({ success: true, data: updatedBinType });
    } catch (error) {
        return res.status(500).json({ success: false, message: "Server Error", error: error.message });
    }
}

async function patchStatus(req, res) {
    try {
        const { status, reason } = req.body;
        if (!status) return res.status(400).json({ success: false, message: "Status is required" });

        const validStatuses = ["active", "inactive", "pending", "modify", "decline"];
        if (!validStatuses.includes(status)) {
            return res.status(400).json({ success: false, message: `Status must be one of: ${validStatuses.join(", ")}` });
        }

        const binType = await BinType.findOne({ _id: req.params.id, deleted_at: null });
        if (!binType) return res.status(404).json({ success: false, message: "Bin type not found" });

        binType.status = status;
        if (reason) binType.reason = reason;

        const updatedBinType = await binType.save();
        return res.status(200).json({ success: true, data: updatedBinType });
    } catch (error) {
        return res.status(500).json({ success: false, message: "Server Error", error: error.message });
    }
}

async function deleteBinType(req, res) {
    try {
        const binType = await BinType.findOne({ _id: req.params.id, deleted_at: null });
        if (!binType) return res.status(404).json({ success: false, message: "Bin type not found" });

        binType.deleted_at = new Date();
        await binType.save();

        return res.status(200).json({ success: true, message: "Bin type deleted successfully" });
    } catch (error) {
        return res.status(500).json({ success: false, message: "Server Error", error: error.message });
    }
}

async function listByProgramme(req, res) {
    try {
        const programmeTypeId = req.params.programmeTypeId;
        if (!isValidObjectId(programmeTypeId)) {
            return res.status(400).json({ success: false, message: "Invalid programme type ID format" });
        }

        const binTypes = await BinType.find({ programmeType: programmeTypeId, deleted_at: null })
            .populate("programmeType")
            .populate("binVariant")
            .populate("binCategory")
            .populate("created_by", "name email");

        return res.status(200).json({ success: true, count: binTypes.length, data: binTypes });
    } catch (error) {
        return res.status(500).json({ success: false, message: "Server Error", error: error.message });
    }
}

async function approve(req, res) {
    try {
        const { entityId } = req.body;
        if (!entityId) return res.status(400).json({ message: "record ID is required." });

        const record = await BinType.findById(entityId);
        if (!record) return res.status(404).json({ message: "record not found." });

        const currentVersion = parseFloat(record.version) || 0.0;
        const updatedVersion = (currentVersion + 0.1).toFixed(1);

        await BinType.findByIdAndUpdate(entityId, { status: "active", version: updatedVersion }, { new: true });
        return res.status(200).json({ message: "record approved successfully." });
    } catch (error) {
        return res.status(500).json({ message: "Internal server error." });
    }
}

async function decline(req, res) {
    try {
        const { entityId, reason } = req.body;
        if (!entityId || !reason) return res.status(400).json({ message: "record ID and reason are required." });

        const record = await BinType.findById(entityId);
        if (!record) return res.status(404).json({ message: "record not found." });

        const currentVersion = parseFloat(record.version) || 0.0;
        const updatedVersion = (currentVersion + 0.1).toFixed(1);

        await BinType.findByIdAndUpdate(entityId, { status: "decline", reason: reason, version: updatedVersion }, { new: true });
        return res.status(200).json({ message: "record declined successfully." });
    } catch (error) {
        return res.status(500).json({ message: "Internal server error." });
    }
}

async function modify(req, res) {
    try {
        const { entityId, instructions } = req.body;
        if (!entityId || !instructions) return res.status(400).json({ message: "record ID and modification instructions are required." });

        const record = await BinType.findById(entityId);
        if (!record) return res.status(404).json({ message: "record not found." });

        const currentVersion = parseFloat(record.version) || 0.0;
        const updatedVersion = (currentVersion + 0.2).toFixed(1);

        await BinType.findByIdAndUpdate(entityId, { status: "modify", reason: instructions, version: updatedVersion }, { new: true });
        return res.status(200).json({ message: "record modification request submitted successfully." });
    } catch (error) {
        return res.status(500).json({ message: "Internal server error." });
    }
}

module.exports = {
    validateObjectId,
    listBinTypes,
    getBinTypeById,
    createBinType,
    updateBinType,
    patchStatus,
    deleteBinType,
    listByProgramme,
    approve,
    decline,
    modify,
};
