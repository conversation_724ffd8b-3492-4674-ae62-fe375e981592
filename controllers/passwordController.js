const bcrypt = require('bcrypt');
const crypto = require('crypto');
const nodemailer = require('nodemailer');
const { sign } = require('jsonwebtoken');

const OtpToken = require('../models/OtpToken');
const User = require('../models/user');

const transporter = nodemailer.createTransport({
  service: 'gmail',
  secure: true,
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

/**
 * Request password reset - generate OTP, store hashed token, send email
 */
async function requestPasswordReset(req, res) {
  try {
    const { email } = req.body;
    if (!email) return res.status(400).json({ message: 'Email is required' });

    const user = await User.findOne({ email: email.toLowerCase() });
    // don't reveal whether user exists
    if (!user) {
      return res.status(200).json({ message: 'If your email exists in our system, you will receive a password reset OTP' });
    }

    const otp = crypto.randomInt(100000, 999999).toString();
    const hashedOtp = await bcrypt.hash(otp, 10);

    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 15);

    await OtpToken.deleteMany({ userId: user._id, type: 'password_reset' });

    await OtpToken.create({
      userId: user._id,
      token: hashedOtp,
      type: 'password_reset',
      expiresAt,
    });

    const mailOptions = {
      from: process.env.EMAIL_FROM,
      to: email,
      subject: 'Password Reset OTP',
      html: `
        <h1>Password Reset</h1>
        <p>You requested a password reset. Use the following OTP to reset your password:</p>
        <h2 style="font-size: 24px; letter-spacing: 2px; text-align: center; padding: 10px; background-color: #f0f0f0; border-radius: 4px;">${otp}</h2>
        <p>This OTP will expire in 15 minutes.</p>
        <p>If you didn't request this, please ignore this email.</p>
      `,
    };

    await transporter.sendMail(mailOptions);

    return res.status(200).json({ message: 'Password reset OTP has been sent to your email' });
  } catch (error) {
    return res.status(500).json({ message: 'An error occurred while processing your request' });
  }
}

/**
 * Reset password using OTP
 */
async function resetPassword(req, res) {
  try {
    const { email, otp, password } = req.body;
    if (!email || !otp || !password) {
      return res.status(400).json({ message: 'Email, OTP, and new password are required' });
    }
    if (password.length < 8) {
      return res.status(400).json({ message: 'Password must be at least 8 characters long' });
    }

    const user = await User.findOne({ email: email.toLowerCase() });
    if (!user) return res.status(404).json({ message: 'User not found' });

    const otpRecord = await OtpToken.findOne({ userId: user._id, type: 'password_reset' }).sort({ createdAt: -1 });
    if (!otpRecord) return res.status(400).json({ message: 'No OTP found. Please request a new one' });

    if (new Date() > otpRecord.expiresAt) {
      await OtpToken.deleteOne({ _id: otpRecord._id });
      return res.status(400).json({ message: 'OTP has expired. Please request a new one' });
    }

    const isOtpValid = await bcrypt.compare(otp, otpRecord.token);
    if (!isOtpValid) return res.status(400).json({ message: 'Invalid OTP' });

    const hashedPassword = await bcrypt.hash(password, 10);
    await User.updateOne({ _id: user._id }, { password: hashedPassword });

    await OtpToken.deleteOne({ _id: otpRecord._id });

    return res.status(200).json({ message: 'Password has been reset successfully' });
  } catch (error) {
    return res.status(500).json({ message: 'An error occurred while resetting your password' });
  }
}

function generateToken(user) {
  return sign({ id: user._id }, process.env.JWT_SECRET, { expiresIn: '1d' });
}

module.exports = {
  requestPasswordReset,
  resetPassword,
  generateToken,
};