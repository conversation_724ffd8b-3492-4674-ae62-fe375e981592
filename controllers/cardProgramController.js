const CardProgram = require('../models/CardProgram');

// Controller to save card program data
const saveCardProgram = async (req, res) => {
    try {
        const cardProgramData = req.body;

        // Create a new instance of the CardProgram model
        const newCardProgram = new CardProgram(cardProgramData);

        // Save to the database
        await newCardProgram.save();

        return res.status(201).json({
            message: 'Card program data saved successfully!',
            data: newCardProgram
        });
    } catch (error) {
        return res.status(500).json({message: 'Error saving card program data', error: error.message});
    }
};




const getCardProgramDetails = async (req, res) => {
    try {
        // Fetch the CardProgram by ID, populating all related fields
        const cardProgram = await CardProgram.findById(req.params.id)
            .populate('company') // Populate 'company' field
            .populate('cardScheme') // Populate 'cardScheme' field
            .populate('programmeType') // Populate 'cardProgrammeType' field
            .populate('programmeManagerType') // Populate 'cardProgrammeType' field
            .populate('binType') // Populate 'binType' field
            .populate('created_by') // Populate 'created_by' field
            .populate('productVersionName')

        if (!cardProgram) {
            return res.status(404).json({ message: 'Card Program not found' });
        }

        // Return the populated card program details
        res.status(200).json({
            message: 'Card Program details fetched successfully!',
            data: cardProgram
        });
    } catch (error) {
        res.status(500).json({ message: 'Error fetching card program details', error: error.message });
    }
};




const updateCardProgram = async (req, res) => {
    try {
        const {id} = req.params; // Get the ID from the request parameters
        const updateData = req.body; // Get the update data from the request body

        // Find the card program by ID and update with new data
        const updatedCardProgram = await CardProgram.findByIdAndUpdate(
            id,
            {$set: updateData}, // Use $set to update only provided fields
            {new: true, runValidators: true} // Return the updated document and validate fields
        );

        if (!updatedCardProgram) {
            return res.status(404).json({message: 'Card program not found'});
        }

        return res.status(200).json({
            message: 'Card program updated successfully!',
            data: updatedCardProgram
        });
    } catch (error) {
        return res.status(500).json({message: 'Error updating card program', error: error.message});
    }
};


const deleteCardProgramme = async (req, res) => {
    try {
        const { id } = req.params; // Get the ID from the request parameters

        // Find the card program by ID and delete it
        const deletedCardProgram = await CardProgram.findByIdAndDelete(id);

        if (!deletedCardProgram) {
            return res.status(404).json({ message: 'Card program not found' });
        }

        return res.status(200).json({
            message: 'Card program deleted successfully!',
            data: deletedCardProgram
        });
    } catch (error) {
        return res.status(500).json({ message: 'Error deleting card program', error: error.message });
    }
};
const getAllCardPrograms = async (req, res) => {
    try {
        // Find all CardProgram entries and populate related fields
        const cardPrograms = await CardProgram.find()
            .populate('company') // Populate 'company' field
            .populate('cardScheme') // Populate 'cardScheme' field
            .populate('programmeType') // Populate 'cardProgrammeType' field
            .populate('programmeManagerType') // Populate 'cardProgrammeType' field
            .populate('binType') // Populate 'binType' field
            .populate('created_by') // Populate 'created_by' field
            .populate('productVersionName')

        if (!cardPrograms || cardPrograms.length === 0) {
            return res.status(404).json({message: 'No card programs found'});
        }

        // Return the list of all card programs with related details populated
        res.status(200).json({
            message: 'Card Programs fetched successfully!',
            data: cardPrograms
        });
    } catch (error) {
        res.status(500).json({message: 'Error fetching card programs', error: error.message});
    }
};





module.exports = {
    getAllCardPrograms,
    getCardProgramDetails,
    saveCardProgram,
    updateCardProgram,deleteCardProgramme
};
