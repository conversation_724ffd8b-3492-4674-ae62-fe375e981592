const CardType = require("../models/CardType");
const User = require("../models/user");
const {createTask} = require("../config/EventHandler"); 

/* Create a new card type */
const createCardType = async (req, res) => {
  try {
    const {
        type,
        code,
        created_by,
        binCode,
        binCodeSuffix,
        binCodePrefix,
        currency,
        binCategory,
        binVariant,
        bin_end,
        bin_start
    } = req.body;

    if (!type || !code) {
        return res.status(400).json({message: "Type and Code are required"});
    }
    const recordCount = await CardType.countDocuments();
    const x = recordCount + 1
    // Calculate the new version number
    const newVersion = x + `.0`;
    const newCardType = new CardType({
        type,
        code, created_by, binCode,
        version: newVersion,
        binCodeSuffix: binCodeSuffix,
        binCodePrefix: binCodePrefix,
        currency: currency,
        binCategory: binCategory,
        binVariant: binVariant,
        bin_end: bin_end,
        bin_start: bin_start
    });

    const p =  await newCardType.save();
    const user = await User.findById(created_by);

    const taskData = {
        refId: p._id,
        type: 'BIN Type',
        title: user.name + ' requested a new BIN Type "' + type +' ' + code +'"',
        date: new Date(),
        user: created_by,
        ipAddress: '************',
    };
    await createTask(taskData)

    res.status(201).json({message: "Card Type created successfully", cardType: newCardType});
} catch (error) {
    res.status(500).json({message: "Server error", error});
}
}

/* Get all card types (not deleted) */
const getCardTypes = async (req, res) => {
  try {
    const cardTypes = await CardType.find({ deleted_at: null }).sort({ created_at: -1 });
    return res.json({ success: true, count: cardTypes.length, data: cardTypes });
  } catch (err) {
    return res.status(500).json({ success: false, message: err.message });
  }
}

/* Soft delete (set deleted_at) */
async function deleteCardType(req, res) {
  try {
    const { id } = req.params;
    const updated = await CardType.findByIdAndUpdate(id, { deleted_at: new Date() }, { new: true });
    if (!updated) return res.status(404).json({ message: "Record not found" });
    return res.json({ message: "Record deleted successfully" });
  } catch (err) {
    return res.status(500).json({ message: "Error deleting Record", error: err.message });
  }
}

/* Approve: set status active and bump version by 0.1 */
async function approve(req, res) {
  try {
    const { entityId } = req.body;
    if (!entityId) return res.status(400).json({ message: "record ID is required." });

    const record = await CardType.findById(entityId);
    if (!record) return res.status(404).json({ message: "record not found." });

    const currentVersion = parseFloat(record.version) || 0.0;
    const updatedVersion = (currentVersion + 0.1).toFixed(1);

    await CardType.findByIdAndUpdate(entityId, { status: "active", version: updatedVersion }, { new: true });
    return res.status(200).json({ message: "record approved successfully." });
  } catch (err) {
    return res.status(500).json({ message: "Internal server error.", error: err.message });
  }
}

/* Decline: set status decline, save reason, bump version by 0.1 */
async function decline(req, res) {
  try {
    const { entityId, reason } = req.body;
    if (!entityId || !reason) return res.status(400).json({ message: "record ID and reason are required." });

    const record = await CardType.findById(entityId);
    if (!record) return res.status(404).json({ message: "record not found." });

    const currentVersion = parseFloat(record.version) || 0.0;
    const updatedVersion = (currentVersion + 0.1).toFixed(1);

    await CardType.findByIdAndUpdate(entityId, { status: "decline", reason, version: updatedVersion }, { new: true });
    return res.status(200).json({ message: "record declined successfully." });
  } catch (err) {
    return res.status(500).json({ message: "Internal server error.", error: err.message });
  }
}

/* Modify: set status modify, save instructions, bump version by 0.2 */
async function modify(req, res) {
  try {
    const { entityId, instructions } = req.body;
    if (!entityId || !instructions) return res.status(400).json({ message: "record ID and modification instructions are required." });

    const record = await CardType.findById(entityId);
    if (!record) return res.status(404).json({ message: "record not found." });

    const currentVersion = parseFloat(record.version) || 0.0;
    const updatedVersion = (currentVersion + 0.2).toFixed(1);

    await CardType.findByIdAndUpdate(entityId, { status: "modify", reason: instructions, version: updatedVersion }, { new: true });
    return res.status(200).json({ message: "record modification request submitted successfully." });
  } catch (err) {
    return res.status(500).json({ message: "Internal server error.", error: err.message });
  }
}

module.exports = {
  createCardType,
  getCardTypes,
  deleteCardType,
  approve,
  decline,
  modify,
};
