const Account = require('../models/Account');
const Onboarding = require('../models/cipOnboarding');

exports.saveOnboardingData = async (req, res) => {
    try { 
        const onboardingData = new Onboarding(req.body);
        await onboardingData.save();
        res.status(201).json({success: true, message: 'Onboarding data saved successfully', data: onboardingData});
    } catch (error) {
        res.status(500).json({success: false, message: 'An error occurred while saving onboarding data'});
    }
};

/**
 * List onboarding applications with optional pagination and simple filters
 */
exports.listOnboardings = async (req, res) => {
  try {
    const { page = 1, limit = 100, status, q } = req.query;
    const skip = (Math.max(Number(page), 1) - 1) * Number(limit);

    const filter = {};
    if (status) filter.status = status;
    if (q) filter.$or = [
      { name: new RegExp(q, 'i') },
      { email: new RegExp(q, 'i') },
      { clientId: new RegExp(q, 'i') }
    ];

    const [items, total] = await Promise.all([
      Account.find(filter).sort({ createdAt: -1 }).skip(skip).limit(Math.min(Number(limit), 1000)),
      Account.countDocuments(filter)
    ]);

    return res.status(200).json({
      success: true,
      meta: { total, page: Number(page), limit: Number(limit) },
      data: items
    });
  } catch (err) {
    return res.status(500).json({ success: false, message: 'Failed to list onboardings', error: err.message });
  }
}

/**
 * Get single onboarding application by ID
 */
exports.getCipOnboardingById = async (req, res) => {
  try {
    const { id } = req.params;
    const item = await Account.findById(id);
    if (!item) return res.status(404).json({ success: false, message: 'Onboarding record not found' });
    return res.status(200).json({ success: true, data: item });
  } catch (err) {
    return res.status(500).json({ success: false, message: 'Failed to fetch onboarding', error: err.message });
  }
}


