const GenericMessage = require("../models/GenericMessage");

// Save any structure into MongoDB	extra error handling for robustness
const saveGenericMessage = async (req, res) => {
    try {
        const newMessage = new GenericMessage({ data: req.body });
        await newMessage.save();
        res.status(201).json({
            success: true,
            message: "Webhook received successfully",
        });
    } catch (err) {
        res.status(500).json({
            success: false,
            error: err.message || "Something went wrong",
        });
    }
};

module.exports = {saveGenericMessage};