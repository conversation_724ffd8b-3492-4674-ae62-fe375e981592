const Log = require('../models/Log');

/* Build filter object from request query */
function buildFilter(query = {}) {
  const { method, status, search, from, to } = query;

  const filter = {
    $and: [
      { url: { $ne: '/api/users/me' } },
      { method: { $ne: 'OPTIONS' } },
      { url: { $not: /^\/api\/logs/ } },
    ],
  };

  // Method filter
  if (method && method !== 'all') {
    filter.method = method;
  }

  // Status filter
  if (status && status !== 'all') {
    if (typeof status === 'string' && status.endsWith('xx')) {
      const statusPrefix = status.charAt(0);
      filter.status = {
        $gte: Number.parseInt(statusPrefix + '00'),
        $lt: Number.parseInt(statusPrefix + '99') + 1,
      };
    } else {
      filter.status = Number.parseInt(status);
    }
  }

  // Date range
  if (from || to) {
    filter.timestamp = {};
    if (from) filter.timestamp.$gte = new Date(from);
    if (to) filter.timestamp.$lte = new Date(to);
  }

  // Search across fields
  if (search) {
    filter.$or = [
      { url: { $regex: search, $options: 'i' } },
      { remoteAddr: { $regex: search, $options: 'i' } },
      { userIdentity: { $regex: search, $options: 'i' } },
      { referer: { $regex: search, $options: 'i' } },
      { userAgent: { $regex: search, $options: 'i' } },
    ];
  }

  return filter;
}

/* GET /api/logs */
async function listLogs(req, res) {
  try {
    const page = Number.parseInt(req.query.page) || 1;
    const limit = Number.parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const filter = buildFilter(req.query);

    const [logs, total] = await Promise.all([
      Log.find(filter).sort({ timestamp: -1 }).skip(skip).limit(limit).lean(),
      Log.countDocuments(filter),
    ]);

    const totalPages = Math.ceil(total / limit);

    return res.json({
      success: true,
      logs,
      total,
      totalPages,
      page,
      limit,
    });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Failed to fetch logs' });
  }
}

/* GET /api/logs/export */
async function exportLogs(req, res) {
  try {
    const filter = buildFilter(req.query);
    const logs = await Log.find(filter).sort({ timestamp: -1 }).lean();

    const fields = [
      'timestamp',
      'method',
      'url',
      'status',
      'remoteAddr',
      'userIdentity',
      'responseTime',
      'responseSize',
      'referer',
    ];

    let csv = fields.join(',') + '\n';

    logs.forEach((log) => {
      const row = fields
        .map((field) => {
          let value = log[field];

          if (field === 'timestamp' && value) value = new Date(value).toISOString();

          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            value = `"${value.replace(/"/g, '""')}"`;
          }

          // ensure undefined/null become empty string in CSV
          if (value === undefined || value === null) value = '';

          return value;
        })
        .join(',');

      csv += row + '\n';
    });

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader(
      'Content-Disposition',
      `attachment; filename="logs-export-${new Date().toISOString().split('T')[0]}.csv"`,
    );

    return res.send(csv);
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Failed to export logs' });
  }
}

/* GET /api/logs/:id */
async function getLogById(req, res) {
  try {
    const log = await Log.findById(req.params.id).lean();
    if (!log) return res.status(404).json({ success: false, message: 'Log not found' });

    if (log.url === '/api/users/me' || log.method === 'OPTIONS' || (log.url || '').startsWith('/api/logs')) {
      return res.status(403).json({ success: false, message: 'Access to this log is restricted' });
    }

    return res.json({ success: true, log });
  } catch (error) {
    return res.status(500).json({ success: false, message: 'Failed to fetch log' });
  }
}

module.exports = {
  listLogs,
  exportLogs,
  getLogById,
};