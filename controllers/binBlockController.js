const BinBlock = require("../models/BinBlocks");
const Cards = require("../models/AccountCard");

async function createBinBlock(req, res) {
    try {
        const { binStart, binEnd, company, programme, product_version, cardRange } = req.body;
        const newBinBlock = new BinBlock({ binStart, binEnd, company, programme, product_version, cardRange });
        await newBinBlock.save();
        res.status(201).json({ message: "BinBlock created successfully", newBinBlock });
    } catch (error) {
        res.status(500).json({ error: "Internal server error" });
    }
}

async function listBinBlocks(req, res) {
    try {
        const binBlocks = await BinBlock.find().populate("company programme product_version");
        res.json(binBlocks);
    } catch (error) {
        res.status(500).json({ error: "Internal server error" });
    }
}

async function globalUsage(req, res) {
    try {
        const all_cards = await Cards.find({});
        const total_cards = all_cards.length;

        const all_bins = await BinBlock.find({});

        let total_bins = 0;

        all_bins.forEach(bin => {
            const binStart = Number.parseInt(bin.binStart.replace(/\D/g, ""));
            const binEnd = Number.parseInt(bin.binEnd.replace(/\D/g, ""));

            if (!isNaN(binStart) && !isNaN(binEnd) && binEnd >= binStart) {
                total_bins += (binEnd - binStart + 1);
            }
        });

        return res.status(200).json({
            totalCards: total_cards,
            totalBins: total_bins
        });

    } catch (error) {
        return res.status(500).json({ message: "Server Error", error });
    }
}

async function binsPerCompany(req, res) {
    try {
        const all_bins = await BinBlock.find()
            .populate("company")
            .populate("product_version");

        const companyBinStats = {};

        for (const bin of all_bins) {
            const binStart = Number.parseInt(bin.binStart.replace(/\D/g, ""));
            const binEnd = Number.parseInt(bin.binEnd.replace(/\D/g, ""));
            const company = bin.company;
            const productVersion = bin.product_version;

            if (!company || isNaN(binStart) || isNaN(binEnd) || binEnd < binStart) continue;

            const binCount = binEnd - binStart + 1;
            const companyId = company._id.toString();
            const companyName = company.company_name || "Unknown";

            if (!companyBinStats[companyId]) {
                companyBinStats[companyId] = {
                    companyId,
                    companyName,
                    totalBins: 0,
                    usedBins: 0
                };
            }

            companyBinStats[companyId].totalBins += binCount;

            if (productVersion && productVersion.version_code) {
                const cardCount = await Cards.countDocuments({ productCode: productVersion.version_code });
                companyBinStats[companyId].usedBins += cardCount;
            }
        }

        res.status(200).json(Object.values(companyBinStats));

    } catch (error) {
        res.status(500).json({ message: "Server Error", error });
    }
}

async function cipByProgrammeId(req, res) {
    try {
        const binBlocks = await BinBlock.find({ programme: req.params.id })
            .populate("company")
            .populate("product_version")
            .populate({
                path: "programme",
                populate: {
                    path: "productVersionName",
                    model: "ProductVersion"
                }
            });

        for (const block of binBlocks) {
            const versionCode = block?.product_version?.version_code;

            if (versionCode) {
                const cards = await Cards.find({ productCode: versionCode });
                block._doc.total_usage = cards.length;
                block._doc.cards = cards;
            } else {
                block._doc.total_usage = 0;
                block._doc.cards = [];
            }
        }
        res.json(binBlocks);
    } catch (error) {
        res.status(500).json({ error: "Internal server error" });
    }
}

async function getBinBlockById(req, res) {
    try {
        const binBlock = await BinBlock.findById(req.params.id).populate("company programme");
        if (!binBlock) {
            return res.status(404).json({ error: "BinBlock not found" });
        }
        res.json(binBlock);
    } catch (error) {
        res.status(500).json({ error: "Internal server error" });
    }
}

async function updateBinBlock(req, res) {
    try {
        const updatedBinBlock = await BinBlock.findByIdAndUpdate(
            req.params.id,
            req.body,
            { new: true, runValidators: true }
        );

        if (!updatedBinBlock) {
            return res.status(404).json({ error: "BinBlock not found" });
        }

        res.json({ message: "BinBlock updated successfully", updatedBinBlock });
    } catch (error) {
        res.status(500).json({ error: "Internal server error" });
    }
}

async function deleteBinBlock(req, res) {
    try {
        const deletedBinBlock = await BinBlock.findByIdAndDelete(req.params.id);
        if (!deletedBinBlock) {
            return res.status(404).json({ error: "BinBlock not found" });
        }
        res.json({ message: "BinBlock deleted successfully" });
    } catch (error) {
        res.status(500).json({ error: "Internal server error" });
    }
}

module.exports = {
    createBinBlock,
    listBinBlocks,
    globalUsage,
    binsPerCompany,
    cipByProgrammeId,
    getBinBlockById,
    updateBinBlock,
    deleteBinBlock,
};