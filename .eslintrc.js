module.exports = {
    root: true,
    env: {
        node: true,
        es2021: true,
    },
    parserOptions: {
        ecmaVersion: 2021,
        sourceType: "module",
    },
    plugins: ["unused-imports"],
    rules: {
        // remove unused imports automatically
        "unused-imports/no-unused-imports": "error",
        // keep normal unused-vars rule but allow underscore-prefixed vars
        "no-unused-vars": ["warn", { "argsIgnorePattern": "^_", "varsIgnorePattern": "^_" }],
    },
};