const Joi = require("joi")

// Centralized validation constants
const VALIDATION_RULES = {
    MAX_LENGTHS: {
        companyName: 200,
        clientCode: 50,
        phoneNumber: 20,
        email: 255,
        nip: 20,
        regon: 20,
        embossedName: 100,
        companyIndustry: 100,
        companyNumber: 50,
        contactName: 100,
        contactRole: 100,
        countryOfIncorporation: 100,
        companyWebsite: 255,
        typeOfBusiness: 100,
        cardUsage: 500,
        businessSector: 100,
        businessPurpose: 1000,
        adminName: 100,
        adminRole: 100,
        adminEmail: 255,
        adminPhone: 20,
        street: 200,
        buildingNumber: 20,
        apartmentNumber: 20,
        city: 100,
        zipCode: 20,
        country: 100,
    },
    REGEX: {
        phone: /^[0-9+\-() ]{7,20}$/,
        email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        url: /^https?:\/\/.+\..+$/,
    }
}

// Address schema
const addressSchema = Joi.object({
    street: Joi.string().required().trim().max(VALIDATION_RULES.MAX_LENGTHS.street),
    building_number: Joi.string().allow("").trim().max(VALIDATION_RULES.MAX_LENGTHS.buildingNumber),
    apartment_number: Joi.string().allow("").trim().max(VALIDATION_RULES.MAX_LENGTHS.apartmentNumber),
    city: Joi.string().required().trim().max(VALIDATION_RULES.MAX_LENGTHS.city),
    state: Joi.string().allow("").trim().max(100),
    postal_code: Joi.string().required().trim().max(VALIDATION_RULES.MAX_LENGTHS.zipCode),
    country: Joi.string().required().trim().max(VALIDATION_RULES.MAX_LENGTHS.country),
})

// Main B2B registration schema
const b2bRegistrationSchema = Joi.object({
    companyName: Joi.string().required().trim().max(VALIDATION_RULES.MAX_LENGTHS.companyName),
    company_name: Joi.string().trim().max(VALIDATION_RULES.MAX_LENGTHS.companyName),
    clientCode: Joi.string().allow("").trim().max(VALIDATION_RULES.MAX_LENGTHS.clientCode),
    phoneNumber: Joi.string().required().trim().max(VALIDATION_RULES.MAX_LENGTHS.phoneNumber),
    company_phone: Joi.string().trim().max(VALIDATION_RULES.MAX_LENGTHS.phoneNumber),
    authPhoneNumber: Joi.string().allow("").trim().max(VALIDATION_RULES.MAX_LENGTHS.phoneNumber),
    email: Joi.string().required().email().lowercase().trim().max(VALIDATION_RULES.MAX_LENGTHS.email),
    company_email: Joi.string().email().lowercase().trim().max(VALIDATION_RULES.MAX_LENGTHS.email),
    nip: Joi.string().allow("").trim().max(VALIDATION_RULES.MAX_LENGTHS.nip),
    regon: Joi.string().allow("").trim().max(VALIDATION_RULES.MAX_LENGTHS.regon),
    embossedName: Joi.string().allow("").trim().max(VALIDATION_RULES.MAX_LENGTHS.embossedName),
    company_industry: Joi.string().allow("").trim().max(VALIDATION_RULES.MAX_LENGTHS.companyIndustry),
    company_number: Joi.string().allow("").trim().max(VALIDATION_RULES.MAX_LENGTHS.companyNumber),
    registration_date: Joi.date().iso().max("now"),
    contact_name: Joi.string().allow("").trim().max(VALIDATION_RULES.MAX_LENGTHS.contactName),
    contact_role: Joi.string().allow("").trim().max(VALIDATION_RULES.MAX_LENGTHS.contactRole),
    country_of_incorporation: Joi.string().allow("").trim().max(VALIDATION_RULES.MAX_LENGTHS.countryOfIncorporation),
    company_website: Joi.string().allow("").uri().trim().max(VALIDATION_RULES.MAX_LENGTHS.companyWebsite),
    type_of_business: Joi.string().allow("").trim().max(VALIDATION_RULES.MAX_LENGTHS.typeOfBusiness),
    card_usage: Joi.string().allow("").trim().max(VALIDATION_RULES.MAX_LENGTHS.cardUsage),
    cardholder_groups: Joi.string().allow("").trim().max(200),
    fund_loading: Joi.number().min(0).allow(""),
    business_sector: Joi.string().allow("").trim().max(VALIDATION_RULES.MAX_LENGTHS.businessSector),
    regions: Joi.string().allow("").trim().max(200),
    countries: Joi.string().allow("").trim().max(200),
    business_purpose: Joi.string().allow("").trim().max(VALIDATION_RULES.MAX_LENGTHS.businessPurpose),
    card_user_groups: Joi.string().allow("").trim().max(200),
    number_of_cards: Joi.number().integer().min(0).allow(""),
    monthly_loading_value: Joi.number().min(0).allow(""),
    admin_name: Joi.string().allow("").trim().max(VALIDATION_RULES.MAX_LENGTHS.adminName),
    admin_role: Joi.string().allow("").trim().max(VALIDATION_RULES.MAX_LENGTHS.adminRole),
    admin_email: Joi.string().allow("").email().lowercase().trim().max(VALIDATION_RULES.MAX_LENGTHS.adminEmail),
    admin_phone: Joi.string().allow("").trim().max(VALIDATION_RULES.MAX_LENGTHS.adminPhone),
    address: addressSchema, // Legacy support
    registered_address: addressSchema,
    operational_address: addressSchema,
})

const validateB2BRegistration = (data) => {
    return b2bRegistrationSchema.validate(data, {
        abortEarly: false,
        allowUnknown: true,
    })
}

module.exports = {
    VALIDATION_RULES,
    b2bRegistrationSchema,
    validateB2BRegistration,
}

