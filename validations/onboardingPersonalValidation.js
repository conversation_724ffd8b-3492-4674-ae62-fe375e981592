const VALIDATION_RULES = require("../utils/validation-constants");
const {
    isValidEmail,
    isValidDate,
    isNotEmpty,
    isValidLength,
    isValidClientCode,
    isValidCountryCode,
    checkAddress
} = require("../utils/validation-utils");

function validateOnboardingPersonal(data) {
    const {
        clientCode,
        personalInfo,
        mothersMaidenName,
        legalId,
        citizenship,
        address,
    } = data;

    const validationErrors = [];

    // Validate client code (mandatory)
    if (!isNotEmpty(clientCode)) {
        validationErrors.push("Client code is required");
    } else if (!isValidClientCode(clientCode)) {
        validationErrors.push("Invalid client code format. Must be in format RYVL-xxxxxxxx with max length 15");
    }

    // Validate personalInfo (mandatory)
    if (!personalInfo) {
        validationErrors.push("Personal information is required");
    } else {
        // First name (mandatory)
        if (!isNotEmpty(personalInfo.firstName)) {
            validationErrors.push("First name is required");
        } else if (!isValidLength(personalInfo.firstName, VALIDATION_RULES.MAX_LENGTHS.firstName)) {
            validationErrors.push(`First name must not exceed ${VALIDATION_RULES.MAX_LENGTHS.firstName} characters`);
        }
        // Second name (optional)
        if (personalInfo.secondName && !isValidLength(personalInfo.secondName, VALIDATION_RULES.MAX_LENGTHS.secondName)) {
            validationErrors.push(`Second name must not exceed ${VALIDATION_RULES.MAX_LENGTHS.secondName} characters`);
        }
        // Last name (mandatory)
        if (!isNotEmpty(personalInfo.lastName)) {
            validationErrors.push("Last name is required");
        } else if (!isValidLength(personalInfo.lastName, VALIDATION_RULES.MAX_LENGTHS.lastName)) {
            validationErrors.push(`Last name must not exceed ${VALIDATION_RULES.MAX_LENGTHS.lastName} characters`);
        }
        // Email (mandatory)
        if (!isNotEmpty(personalInfo.email)) {
            validationErrors.push("Email is required");
        } else if (!isValidEmail(personalInfo.email)) {
            validationErrors.push("Invalid email format");
        } else if (!isValidLength(personalInfo.email, VALIDATION_RULES.MAX_LENGTHS.email)) {
            validationErrors.push(`Email must not exceed ${VALIDATION_RULES.MAX_LENGTHS.email} characters`);
        }
        // Birth date (mandatory)
        if (!isNotEmpty(personalInfo.birthDate)) {
            validationErrors.push("Date of birth is required");
        } else if (!isValidDate(personalInfo.birthDate)) {
            validationErrors.push("Invalid date of birth format. Must be in format DD MMM YYYY");
        }
    }

    // Validate mothers maiden name (optional)
    if (mothersMaidenName && !isValidLength(mothersMaidenName, VALIDATION_RULES.MAX_LENGTHS.mothersMaidenName)) {
        validationErrors.push(`Mother's maiden name must not exceed ${VALIDATION_RULES.MAX_LENGTHS.mothersMaidenName} characters`);
    }

    // Validate legal ID (optional)
    if (legalId && !isValidLength(legalId, VALIDATION_RULES.MAX_LENGTHS.legalId)) {
        validationErrors.push(`Legal ID must not exceed ${VALIDATION_RULES.MAX_LENGTHS.legalId} characters`);
    }

    const citizenshipCode = getCountryNumber(citizenship);
    // Validate citizenship (mandatory)
    if (!isNotEmpty(citizenshipCode)) {
        validationErrors.push("Citizenship is required");
    } else if (!isValidLength(citizenshipCode, VALIDATION_RULES.MAX_LENGTHS.citizenship)) {
        validationErrors.push(`Citizenship must not exceed ${VALIDATION_RULES.MAX_LENGTHS.citizenship} characters`);
    } else if (!isValidCountryCode(citizenshipCode)) {
        validationErrors.push("Invalid citizenship country code");
    }

    const birthCountryCode = getCountryNumber(personalInfo?.birthCountry);
    // Validate birth country (mandatory)
    if (!isNotEmpty(birthCountryCode)) {
        validationErrors.push("Birth country is required");
    } else if (!isValidLength(birthCountryCode, VALIDATION_RULES.MAX_LENGTHS.birthCountry)) {
        validationErrors.push(`Birth country must not exceed ${VALIDATION_RULES.MAX_LENGTHS.birthCountry} characters`);
    } else if (!isValidCountryCode(birthCountryCode)) {
        validationErrors.push("Invalid birth country code");
    }

    // Validate address (mandatory)
    if (!address) {
        validationErrors.push("Address information is required");
    } else {
        validationErrors.push(...checkAddress(address, VALIDATION_RULES.MAX_LENGTHS));
    }

    return validationErrors;
}

module.exports = { validateOnboardingPersonal };
