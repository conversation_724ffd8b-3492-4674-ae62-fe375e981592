// middleware/file-upload.js
const multer = require("multer");
const NodeClam = require('clamscan');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// Multer config (in-memory)
const storage = multer.memoryStorage();

const fileFilter = (req, file, cb) => {
    if (
        ["image/jpeg", "image/png", "image/jpg", "application/pdf"].includes(file.mimetype)
    ) {
        cb(null, true);
    } else {
        cb(new Error("Unsupported file type"), false);
    }
};

const upload = multer({
    storage,
    limits: { fileSize: 5 * 1024 * 1024 }, // 5 MB
    fileFilter,
});

// Malware scan
async function scanBufferForMalware(buffer) {
    const clamscan = await new NodeClam().init();
    const { isInfected, viruses } = await clamscan.scanBuffer(buffer);
    return { isInfected, viruses };
}

// Save file
function saveBufferToDisk(buffer, originalName) {
    const ext = path.extname(originalName);
    const filename = uuidv4() + ext;
    const uploadDir = path.join(__dirname, '../uploads');
    if (!fs.existsSync(uploadDir)) fs.mkdirSync(uploadDir, { recursive: true });
    const filePath = path.join(uploadDir, filename);
    fs.writeFileSync(filePath, buffer);
    return filePath;
}

// ✅ Proper single middleware
const secureUpload = (req, res, next) => {
    const handler = upload.single("file");

    handler(req, res, async (err) => {
        if (err) {
            return res.status(400).json({ error: "File upload failed", details: err.message });
        }

        if (!req.file) {
            return res.status(400).json({ error: "No file uploaded" });
        }

        try {
            const { isInfected, viruses } = await scanBufferForMalware(req.file.buffer);
            if (isInfected) {
                return res.status(400).json({ error: "Malware detected", viruses });
            }

            req.savedFilePath = saveBufferToDisk(req.file.buffer, req.file.originalname);
            next();
        } catch (e) {
            return res.status(500).json({ error: "File scan failed", details: e.message });
        }
    });
};

module.exports = { secureUpload };
