const Log = require("../models/Log")

/**
 * Express middleware to log HTTP requests and responses
 */
const logger = async (req, res, next) => {
    // Store original methods to capture response
    const originalSend = res.send
    const originalJson = res.json
    const originalEnd = res.end

    // Start time for response time calculation
    const start = Date.now()

    // Response body container
    let responseBody = {}

    // Override send method to capture response body
    res.send = (body) => {
        let responseBodyStr = body

        // Try to parse JSON if it's a string
        if (typeof body === "string") {
            try {
                responseBodyStr = JSON.parse(body)
            } catch (e) {
                // Not JSON, keep as string
            }
        }

        responseBody = responseBodyStr
        return originalSend.apply(res, arguments)
    }

    // Override json method to capture response body
    res.json = (body) => {
        responseBody = body
        return originalJson.apply(res, arguments)
    }

    // After response is sent
    res.on("finish", async () => {
        try {
            // Skip logging OPTIONS requests entirely
            if (req.method === "OPTIONS") {
                return
            }

            // Skip logging requests to the logs API itself to avoid recursive logging
            const url = req.originalUrl || req.url
            if (url.startsWith("/api/logs")) {
                return
            }

            // Calculate response time
            const responseTime = `${Date.now() - start}ms`

            // Check if this is a sensitive endpoint
            const isSensitiveEndpoint = url === "/api/users/me"

            // If you want to still log them but with redacted content
            const requestBodyToLog = isSensitiveEndpoint ? { redacted: "Sensitive information" } : req.body || {}
            const responseBodyToLog = isSensitiveEndpoint ? { redacted: "Sensitive information" } : responseBody || {}

            // Create log entry
            const logEntry = new Log({
                remoteAddr: req.ip || req.connection.remoteAddress,
                userIdentity: req.user ? req.user.id || req.user.username : "Anonymous",
                timestamp: new Date(),
                method: req.method,
                url: url,
                httpVersion: `HTTP/${req.httpVersion}`,
                status: res.statusCode,
                responseSize: Buffer.byteLength(JSON.stringify(responseBody), "utf8") + " bytes",
                referer: req.get("referer") || "Direct Access",
                userAgent: req.get("user-agent") || "Unknown",
                responseTime,
                requestBody: requestBodyToLog,
                responseBody: responseBodyToLog,
            })

            // Save log to database
            await logEntry.save()
        } catch (error) {}
    })

    next()
}

module.exports = logger
