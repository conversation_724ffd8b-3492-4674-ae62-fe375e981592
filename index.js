require('dotenv').config();
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const path = require("path");
const authenticateToken = require("./middleware/authMiddleware");
const morgan = require("morgan");
const BodyParser = require('body-parser');
const Log = require("./models/Log");
const mongoSanitize = require("express-mongo-sanitize");



// Routes
const authRoutes = require('./routes/authRoutes');
const userRoutes = require('./routes/userRoutes');
const roleRoutes = require('./routes/roleRoutes');
const companyRoutes = require('./routes/companyRoutes');
const cardSchemeRoutes = require("./routes/cardSchemeRoutes");
const cardBinRoutes = require("./routes/cardBinRoutes");
const cardProgrammeTypeRoutes = require("./routes/cardProgrammeTypeRoutes");
const cardTypeRoutes = require("./routes/cardTypeRoutes");
const productVersion = require("./routes/productVersionRoutes");
const productCurrencyRoutes = require("./routes/productCurrencyRoutes");
const cip = require("./routes/cardProgramRoutes");
const customerType = require("./routes/CustomerType");
const countryRoutes = require("./routes/countryRoutes");
const onboardingPersonal = require("./routes/onboardingPersonalRoute");
const binRangeRoutes = require("./routes/BinRangeRoutes");
const ClientRoutes = require("./routes/clientRoutes");
const EventsRoutes = require("./routes/eventsRouters");
const CardImage = require("./routes/CardImagesRoutes");
const ItemCardRoutes = require("./routes/ItCardApiRoutes");
const BinCategoryRoutes = require("./routes/BinCategoryRoutes");
const BinVariantsRoutes = require("./routes/BinVariantsRoutes");
const BinUsageRoutes = require("./routes/BinUsageRoutes");
const ProgrammeManagerTypeRoutes = require("./routes/ProgrammeManagerTypeRoutes");
const DataApis = require("./routes/SensetiveDataRoutes");
const openCompanyApi = require("./routes/companyOpenRoutes");
const PinRoute = require("./routes/pinRoute");
const PasswordRoutes = require("./routes/forger-password");
const BinBlocksRoute = require("./routes/binBlockRoutes");
const ILRoutes = require("./ILRoutes/IntigrationLayerRoutes");
const ILB2BRoutes = require("./ILRoutes/ILB2BRoutes");
const LocalRoutes = require("./LocalRoutes/LocalApiRoutes");
const ProgrammeTypeRoutes = require("./routes/ProgrammeTypeRoutes");
const LogsRoutes = require("./routes/LogRoutes");
const zonesRoutes = require("./routes/ZoneRoutes");
const companyContactRoutes = require("./routes/companyContact");
const zoneMethodConfigsRoutes = require("./routes/zoneMethodConfigs");
const webhookRoutes = require("./routes/WebhookRoute");
const regionRoutes = require("./routes/regionRoutes");
const ppRoutes = require("./routes/polishPostRoutes");
const userActivityRoutes = require("./routes/activityRoutes");
const webhookLogRoutes = require("./routes/webhook-logs-routes");
const legacyRoutes = require("./routes/LegecyApis");
const CorporateRoutes = require("./routes/CorporateRoutes");
const b2bRoutes = require("./routes/b2bRoutes");
const binTypeRoutes = require("./routes/BinTypeRoutes");
const feeApis = require("./routes/ItCardFeeApiRoutes");
const notifications = require("./routes/NotificationsRoute");
const CardholderRoutes = require("./routes/CardholderRoutes");
const helmet = require("helmet");
const rateLimit = require("express-rate-limit");

const app = express();

// Middleware50mb
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));
app.use("/uploads", express.static(path.join(__dirname, "uploads")));
app.use(mongoSanitize());

// Enable body-parser
app.use(BodyParser.urlencoded({ extended: false }));
app.use(BodyParser.json());
//
// ✅ Security headers with helmet
app.use(
    helmet({
        contentSecurityPolicy: {
            directives: {
                defaultSrc: ["'self'"],
                scriptSrc: ["'self'"],
                objectSrc: ["'none'"],
                imgSrc: ["'self'", "data:", "https:"],
                upgradeInsecureRequests: [],
            },
        },
        // frameguard: { action: "deny" }, // X-Frame-Options: DENY
        // referrerPolicy: { policy: "no-referrer" },
    })
);

// ✅ Rate limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 1000, // limit each IP to 100 requests per window
    standardHeaders: true, // Return rate limit info in headers
    legacyHeaders: false,
});
app.use(limiter);


// MongoDB connection
mongoose.connect(process.env.DATABASE_URL, {})
    .then(() => console.log("connected to mongo db"))
    .catch((e) => console.error(e));

// CORS Configuration
const allowedOrigins = [
    'https://apicip.ryvyl.eu',
    'http://apicip.ryvyl.eu',
    'https://cip.ryvyl.eu',
    'http://localhost:3001',
    'http://localhost:3000',
    'https://ryvyl-cards.vercel.app',
    'https://ryvyl-cards-zxql.vercel.app',
    'https://stagingcip.ryvyl.eu','https://betacip.ryvyl.eu'
];

app.use(cors({
    origin: function (origin, callback) {
        if (!origin) return callback(null, true);
        if (allowedOrigins.indexOf(origin) === -1) {
            return callback(new Error('Not allowed by CORS'));
        }
        return callback(null, true);
    },
    credentials: true
}));

// ✅ Middleware to Capture Response Body
app.use((req, res, next) => {
    const originalSend = res.send;
    res.send = function (body) {
        res.responseBody = body;
        originalSend.call(this, body);
    };
    next();
});

// ✅ Disable logging on localhost
const isLocalhost = () => {
    return ['development', 'localhost'].includes(process.env.NODE_ENV) || process.env.HOST === 'localhost';
};

if (!isLocalhost()) {
    morgan.token("id", (req) => req.id || "N/A");
    morgan.token("resBody", (req, res) => res.responseBody || "N/A");
    morgan.token("reqBody", (req) => JSON.stringify(req.body) || "N/A");
    morgan.token("referer", (req) => req.headers.referer || "Direct Access");

    app.use(morgan(function (tokens, req, res) {
        const clientTime = req.headers['x-client-time'];
        const serverTime = new Date();

        const logData = {
            remoteAddr: tokens["remote-addr"](req, res),
            userIdentity: tokens["remote-user"](req, res) || "Anonymous",
            timestamp: serverTime,
            clientTime: clientTime || "Not Provided",
            method: tokens.method(req, res),
            url: tokens.url(req, res),
            httpVersion: tokens["http-version"](req, res),
            status: parseInt(tokens.status(req, res), 10),
            responseSize: tokens.res(req, res, "content-length") || "0",
            referer: tokens.referer(req, res),
            userAgent: tokens["user-agent"](req, res),
            responseTime: `${tokens["response-time"](req, res)} ms`,
            requestBody: req.body,
            responseBody: res.responseBody ? JSON.parse(res.responseBody) : {},
        };

        Log.create(logData)
            .then(() => null)
            .catch(() => null);

        return JSON.stringify(logData);
    }));
} else {}

// Public Route
app.get('/', (req, res) => res.send('Welcome to Ryvyl'));

// Public APIs
app.use('/api/auth', authRoutes);

// Authenticated APIs
app.use('/api/users', authenticateToken, userRoutes);
app.use('/api/activity', authenticateToken, userActivityRoutes);
app.use('/api/roles', authenticateToken, roleRoutes);
app.use('/api/company', authenticateToken, companyRoutes);
app.use("/api/cardScheme", authenticateToken, cardSchemeRoutes);
app.use("/api/cardBin", authenticateToken, cardBinRoutes);
app.use("/api/cardProgram", authenticateToken, cardProgrammeTypeRoutes);
app.use("/api/card-types", authenticateToken, cardTypeRoutes);
app.use("/api/webhook-logs", authenticateToken, webhookLogRoutes);
app.use("/api/customer-types", authenticateToken, customerType);
app.use("/api/product-versions", authenticateToken, productVersion);
app.use("/api/product-currencies", authenticateToken, productCurrencyRoutes);
app.use("/api/cip", authenticateToken, cip);
app.use("/api/cards", authenticateToken, ItemCardRoutes);
app.use("/api/countries", authenticateToken, countryRoutes);
app.use("/api/onboarding/personal", onboardingPersonal);
app.use("/api/cardholder", authenticateToken, CardholderRoutes);
app.use("/api/client",authenticateToken, ClientRoutes);
app.use("/api/events", authenticateToken, EventsRoutes);
app.use("/api/images", authenticateToken, CardImage);
app.use("/api/bin-category", authenticateToken, BinCategoryRoutes);
app.use("/api/bin-variant", authenticateToken, BinVariantsRoutes);
app.use("/api/bin-usage", authenticateToken, BinUsageRoutes);
app.use("/api/programme-manager-types", authenticateToken, ProgrammeManagerTypeRoutes);
app.use("/api/pin", authenticateToken, PinRoute);
app.use("/api/bin", authenticateToken, BinBlocksRoute);
app.use("/api/programme-type", authenticateToken, ProgrammeTypeRoutes);
app.use("/api/bin-types", authenticateToken, binTypeRoutes);
app.use("/api/bin-range", authenticateToken, binRangeRoutes);

app.use("/api/logs", authenticateToken, LogsRoutes);
app.use("/api/legacy", authenticateToken, legacyRoutes);
app.use("/api/zones", authenticateToken, zonesRoutes);
app.use("/api/contacts", authenticateToken, companyContactRoutes);
app.use("/api/regions", authenticateToken, regionRoutes);
app.use("/api/pp", authenticateToken, ppRoutes);
app.use("/api/corporate", authenticateToken, CorporateRoutes);
app.use("/api/b2b", b2bRoutes);

app.use("/api/zone-method-configs", zoneMethodConfigsRoutes);
app.use("/webhook", webhookRoutes);
app.use("/api/data", authenticateToken, DataApis);
app.use("/api/companies", openCompanyApi);
app.use("/api/password", PasswordRoutes);
app.use("/api/il", authenticateToken, ILRoutes);
app.use("/api/il/b2b", authenticateToken, ILB2BRoutes);
app.use("/api/local", authenticateToken, LocalRoutes);
app.use("/api/fee", authenticateToken, feeApis);
app.use("/api/notifications", authenticateToken, notifications);


// Upload Route
const uploadRoutes = require('./routes/uploadRoutes');
app.use('/api', uploadRoutes);


// Error Handling Middleware
app.use((err, req, res, next) => {
            console.error('Error:', err);
            res.status(500).json({
                success: false,
                message: err.message || "Server Error",
                stack: process.env.NODE_ENV === "production" ? undefined : err.stack,
                error: process.env.NODE_ENV === "production" ? undefined : err,
            });
        });
// Start server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {});
