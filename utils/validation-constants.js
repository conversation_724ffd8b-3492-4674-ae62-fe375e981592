// Constants for validation rules
const VALIDATION_RULES = {
  // Document types
  ID_DOCUMENT_TYPES: ["ID", "PASSPORT", "RESIDENCE_CARD", "DRIVING_LICENSE", "MINOR_WITHOUT_ID", "OTHER"],

  // Max lengths
  MAX_LENGTHS: {
    clientCode: 15,
    firstName: 40,
    secondName: 40,
    lastName: 40,
    mothersMaidenName: 40,
    birthDate: 12,
    birthCountry: 40,
    legalId: 11,
    citizenship: 40,
    email: 40,
    phoneNumber: 16,
    authPhoneNumber: 16,
    address: 100,
    street: 100,
    buildingNumber: 40,
    apartmentNumber: 10,
    city: 40,
    stateProvince: 40,
    zipCode: 10,
    country: 3,
    idDocumentType: 20,
    idDocumentNumber: 20,
    idDocumentIssueDate: 12,
    idDocumentExpiryDate: 12,
    idDocumentIssuingCountry: 40,
    idDocumentAuthority: 40,
    taxCountry: 2,
    taxIdNumber: 40,
    applicationId: 40,
    riskLevel: 3,
    riskStatus: 9,
    applicationStatus: 10,
    applicationDate: 22,
  },

  // Risk status values
  RISK_STATUS_VALUES: ["LOW", "MEDIUM", "HIGH", "VERY HIGH"],

  // Application status values
  APPLICATION_STATUS_VALUES: ["APPROVED", "REJECTED", "PENDING"],
}

module.exports = VALIDATION_RULES

