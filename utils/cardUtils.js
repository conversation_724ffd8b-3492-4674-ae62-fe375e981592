// utils/cardUtils.js
const crypto = require("crypto");
const {sendPostRequest, sendGetRequest} = require("../config/ApiInstense");


async function getCard(cardId) {

    // API URL
    const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/" + cardId;
     try {
        return await sendGetRequest(url)

    } catch (error) {
         return "Error While Getting Card ";
     }
}
async function getDecryptedCardNumber(cardId) {
    try {
        // Generate EC key pair using SECP256R1
        const { privateKey, publicKey } = crypto.generateKeyPairSync('ec', {
            namedCurve: 'prime256v1',
            publicKeyEncoding: { type: 'spki', format: 'der' }
        });

        const publicKeyBase64 = Buffer.from(publicKey).toString('base64');

        const values = {
            resource: "CARD_NUMBER",
            securityKey: publicKeyBase64
        };

        const url = `https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/${cardId}/auth`;

        const apiResponse = await sendPostRequest(url, values);

        return { success: true, card: apiResponse };
    } catch (error) {
        return { success: false, message: error.message };
    }
}

module.exports = {
    getDecryptedCardNumber,getCard
};
