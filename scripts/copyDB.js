// copy-databases.ts
const { MongoClient } = require("mongodb");

const SOURCE_URI = "";
const TARGET_URI = "";

const DATABASES_TO_COPY = ["name"];

/**
 * Copy collection structure (indexes + validation rules)
 */
async function copyCollectionStructure(sourceDb, targetDb, collName) {
  const sourceColl = sourceDb.collection(collName);

  // Get source collection options (includes validator)
  const sourceInfo = await sourceDb.listCollections({ name: collName }).next();

  // Drop existing collection if exists
  const existing = await targetDb.listCollections({ name: collName }).next();
  if (existing) {
    await targetDb.collection(collName).drop();
  }

  // Recreate with validation if present
  if (sourceInfo?.options?.validator) {
    await targetDb.createCollection(collName, {
      validator: sourceInfo.options.validator,
      validationLevel: sourceInfo.options.validationLevel,
      validationAction: sourceInfo.options.validationAction,
    });
    console.log(`  🛡️ Validation rules copied for ${collName}`);
  } else {
    await targetDb.createCollection(collName);
  }

  // Copy indexes
  const indexes = await sourceColl.indexes();
  for (const idx of indexes) {
    if (idx.name === "_id_") continue; // skip default index

    // Filter out null/undefined values
    const options = {};
    for (const [k, v] of Object.entries(idx)) {
      if (
        !["key", "name", "v"].includes(k) && // exclude metadata
        v !== null &&
        v !== undefined
      ) {
        options[k] = v;
      }
    }

    await targetDb.collection(collName).createIndex(idx.key, options);
    console.log(`  ⚙️ Index copied: ${idx.name}`);
  }
}

/**
 * Copy documents in batches
 */
async function copyDocuments(sourceDb, targetDb, collName) {
  const sourceColl = sourceDb.collection(collName);
  const targetColl = targetDb.collection(collName);

  const cursor = sourceColl.find();
  const batch = [];
  let count = 0;

  while (await cursor.hasNext()) {
    const doc = await cursor.next();
    if (doc) batch.push(doc);

    if (batch.length >= 1000) {
      await targetColl.insertMany(batch, { ordered: false });
      count += batch.length;
      batch.length = 0;
    }
  }

  if (batch.length > 0) {
    await targetColl.insertMany(batch, { ordered: false });
    count += batch.length;
  }

  console.log(`  ✅ ${count} documents copied into ${collName}`);
}

/**
 * Copy entire database
 */
async function copyDatabase(sourceClient, targetClient, dbName) {
  console.log(`\n📦 Copying database: ${dbName}`);

  const sourceDb = sourceClient.db(dbName);
  const targetDb = targetClient.db(dbName);

  const collections = await sourceDb.listCollections().toArray();

  for (const { name: collName } of collections) {
    console.log(`\n🔄 Copying collection: ${collName}`);
    await copyCollectionStructure(sourceDb, targetDb, collName);
    await copyDocuments(sourceDb, targetDb, collName);
  }

  console.log(`\n🎉 Database ${dbName} copied successfully!`);
}

/**
 * Main
 */
async function main() {
  const sourceClient = new MongoClient(SOURCE_URI);
  const targetClient = new MongoClient(TARGET_URI);

  try {
    await sourceClient.connect();
    await targetClient.connect();

    for (const dbName of DATABASES_TO_COPY) {
      await copyDatabase(sourceClient, targetClient, dbName);
    }

    console.log("\n✅ All databases copied successfully!");
  } catch (err) {
    console.error("❌ Error:", err);
  } finally {
    await sourceClient.close();
    await targetClient.close();
  }
}

main();
