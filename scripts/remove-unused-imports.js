module.exports = function(fileInfo, api) {
  const j = api.jscodeshift;
  const root = j(fileInfo.source);

  // helper: count identifier uses excluding declaration locations
  function countUses(name) {
    return root
      .find(j.Identifier, { name })
      .filter(p => {
        const parent = p.parent.node;

        // skip the identifier that is part of an import specifier or the declarator id itself
        if (parent.type === 'ImportSpecifier' || parent.type === 'ImportDefaultSpecifier' || parent.type === 'ImportNamespaceSpecifier') return false;
        if (parent.type === 'VariableDeclarator' && parent.id === p.node) return false;
        // skip object pattern property keys in destructuring (they are local names too)
        if (parent.type === 'Property' && parent.key === p.node && parent.parent && parent.parent.type === 'ObjectPattern') return false;

        return true;
      })
      .size();
  }

  // Handle ES imports
  root.find(j.ImportDeclaration).forEach(path => {
    const node = path.node;
    // process each specifier
    node.specifiers = node.specifiers.filter(spec => {
      const localName = spec.local && spec.local.name;
      if (!localName) return true; // keep if unexpected
      return countUses(localName) > 0;
    });

    // remove import if no specifiers left
    if (node.specifiers.length === 0) {
      j(path).remove();
    } else {
      j(path).replaceWith(node);
    }
  });


  root.find(j.VariableDeclarator, {
    init: node => node && node.type === 'CallExpression' && node.callee && node.callee.name === 'require'
  }).forEach(path => {
    const id = path.node.id;


    if (id.type === 'ObjectPattern') {
      const remaining = id.properties.filter(prop => {
        const localName = (prop.value && prop.value.name) || (prop.key && prop.key.name);
        return localName && countUses(localName) > 0;
      });

      if (remaining.length === 0) {
        // remove the whole declarator
        const parentDecl = path.parent.node; // VariableDeclaration
        // if only one declarator, remove the whole declaration
        if (parentDecl.declarations && parentDecl.declarations.length === 1) {
          j(path.parent).remove();
        } else {
          j(path).remove();
        }
      } else {
        id.properties = remaining;
        j(path).replaceWith(path.node);
      }
      return;
    }


    if (id.type === 'Identifier') {
      const name = id.name;
      if (countUses(name) === 0) {
        const parentDecl = path.parent.node; // VariableDeclaration
        if (parentDecl.declarations && parentDecl.declarations.length === 1) {
          j(path.parent).remove();
        } else {
          j(path).remove();
        }
      }
    }
  });

  return root.toSource({ quote: 'single' });
};