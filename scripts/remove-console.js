module.exports = function(fileInfo, api) {
    const j = api.jscodeshift;
    const root = j(fileInfo.source);

    // remove standalone console.* statements
    root.find(j.ExpressionStatement, {
        expression: {
            callee: {
                object: {name: 'console'}
            }
        }
    }).remove();

    // replace console.* inside other expressions with null (keeps code valid)
    root.find(j.CallExpression, {
        callee: {
            object: { name: 'console' }
        }
    }).forEach(path => {
        j(path).replaceWith(j.literal(null));
    });

    return root.toSource();
};