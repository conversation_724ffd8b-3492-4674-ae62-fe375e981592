/**
 * Remove commented-out code but keep regular comments.
 *
 * Usage:
 *  - Dry run (shows files that would change):
 *      node scripts/remove-commented-code.js
 *  - Apply changes (overwrite files):
 *      node scripts/remove-commented-code.js --apply
 *
 * Note: Run from project root. Review changes (git diff) before committing.
 */
const fs = require('fs');
const path = require('path');

const ROOT = path.resolve(__dirname, '..');
const EXCLUDE_DIRS = ['node_modules', '.git', 'dist', 'build'];

const CODE_LIKE_PATTERNS = [
  /;\s*$/,                      // trailing semicolon
  /\bconst\b|\blet\b|\bvar\b/,  // var/let/const
  /\bfunction\b/,               // function keyword
  /\bclass\b/,                  // class
  /=>/,                         // arrow function
  /\breturn\b/,                 // return
  /=\s*[^=]/,                   // assignment
  /\brequire\s*\(/,             // require(
  /\bmodule\.exports\b/,        // module.exports
  /\bexports\./,                // exports.
  /\bimport\s+.+\s+from\b/,     // import ... from
  /\([^\)]*\)\s*=>/,            // (args) => 
  /console\.(log|warn|error)\s*\(/, // console.log(
  /\.\w+\(/,                    // object.method(
  /{\s*$/,                      // opening brace at line end
  /\bnew\s+[A-Z]/               // new Constructor
];

function isCodeLike(line) {
  const trimmed = line.trim();
  if (!trimmed) return false;

  // If line contains mostly symbols or code tokens, treat as code.
  const codeScore = CODE_LIKE_PATTERNS.reduce((acc, re) => acc + (re.test(trimmed) ? 1 : 0), 0);
  if (codeScore > 0) return true;

  // If it looks like a single identifier or short text, not code.
  // If contains many non-letter characters, consider it code.
  const nonAlpha = (trimmed.match(/[^a-zA-Z0-9\s_'"()`\[\]\.]/g) || []).length;
  const alpha = (trimmed.match(/[a-zA-Z]/g) || []).length;
  if (alpha === 0 && nonAlpha > 0) return true;

  // If line contains parentheses with commas or semicolons inside -> code
  if (/\(.*[;,].*\)/.test(trimmed)) return true;

  return false;
}

function processFile(filePath, apply) {
  const src = fs.readFileSync(filePath, 'utf8');
  let out = src;

  // 1) Remove commented single-lines that are code-like.
  const lines = out.split(/\r?\n/);
  const newLines = [];
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const singleCommentMatch = line.match(/^(\s*)\/\/(.*)$/);
    if (singleCommentMatch) {
      const [commentText] = singleCommentMatch;
      if (isCodeLike(commentText)) {
        // remove the commented code line (replace with empty line to preserve line numbers)
        newLines.push('');
        continue;
      }
    }
    newLines.push(line);
  }
  out = newLines.join('\n');

  // 2) Remove block comments that appear to be mostly code.
  // Find block comments and decide by ratio of code-like lines inside.
  out = out.replace(/\/\*[\s\S]*?\*\//g, (block) => {
    const inner = block.slice(2, -2);
    const blockLines = inner.split(/\r?\n/).map(l => l.replace(/^\s*\*?/, '')); // strip leading *
    if (blockLines.length === 0) return block;

    let codeLikeCount = 0;
    let nonEmpty = 0;
    for (const l of blockLines) {
      const t = l.trim();
      if (t === '') continue;
      nonEmpty++;
      if (isCodeLike(t)) codeLikeCount++;
    }

    // If more than 50% non-empty lines look like code, remove the block entirely.
    if (nonEmpty > 0 && codeLikeCount / nonEmpty >= 0.5) {
      // preserve a single line comment to keep spacing (optional)
      return '';
    }
    return block; // keep the comment block
  });

  if (out !== src) {
    if (apply) {
      fs.writeFileSync(filePath, out, 'utf8');
    }
    return true;
  }
  return false;
}

function walk(dir, apply) {
  const changedFiles = [];
  const entries = fs.readdirSync(dir, { withFileTypes: true });
  for (const entry of entries) {
    if (EXCLUDE_DIRS.includes(entry.name)) continue;
    const full = path.join(dir, entry.name);
    if (entry.isDirectory()) {
      walk(full, apply).forEach(f => changedFiles.push(f));
    } else if (entry.isFile() && full.endsWith('.js')) {
      try {
        const changed = processFile(full, apply);
        if (changed) changedFiles.push(full);
      } catch (err) {
        console.error('Error processing', full, err.message);
      }
    }
  }
  return changedFiles;
}

const apply = process.argv.includes('--apply');
const changed = walk(ROOT, apply);

if (changed.length === 0) {
  console.log('No commented code found.');
} else {
  console.log(`${apply ? 'Updated' : 'Would update'} ${changed.length} file(s):`);
  changed.forEach(f => console.log(' -', path.relative(ROOT, f)));
  if (!apply) {
    console.log('\nRun with --apply to overwrite the files.');
  } else {
    console.log('\nFiles updated. Review changes (git diff) before committing.');
  }
}