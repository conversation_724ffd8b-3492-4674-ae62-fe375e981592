module.exports = function(fileInfo, api) {
  const j = api.jscodeshift;
  const root = j(fileInfo.source);

  // collect all declared local names (imports / requires / vars)
  const declared = new Set();
  const used = new Map(); // name -> count

  // mark usage
  root.find(j.Identifier).forEach(p => {
    const name = p.node.name;
    const parent = p.parent.node;

    // skip declaration identifiers
    if (
      parent.type === 'ImportSpecifier' ||
      parent.type === 'ImportDefaultSpecifier' ||
      parent.type === 'ImportNamespaceSpecifier' ||
      parent.type === 'VariableDeclarator' && parent.id === p.node ||
      parent.type === 'Property' && parent.value === p.node && parent.parent && parent.parent.type === 'ObjectPattern' ||
      parent.type === 'FunctionDeclaration' && parent.id === p.node ||
      (parent.type === 'ClassDeclaration' && parent.id === p.node)
    ) {
      return;
    }

    // count as usage
    used.set(name, (used.get(name) || 0) + 1);
  });

  // collect declarations from imports
  root.find(j.ImportDeclaration).forEach(path => {
    path.node.specifiers.forEach(spec => {
      if (spec.local && spec.local.name) declared.add(spec.local.name);
    });
  });


  root.find(j.VariableDeclarator).forEach(path => {
    const id = path.node.id;
    const init = path.node.init;

    if (id.type === 'Identifier') {
      declared.add(id.name);
    } else if (id.type === 'ObjectPattern') {
      id.properties.forEach(prop => {
        const localName = (prop.value && prop.value.name) || (prop.key && prop.key.name);
        if (localName) declared.add(localName);
      });
    }


    if (init && init.type === 'MemberExpression' && init.object && init.object.type === 'CallExpression' &&
        init.object.callee && init.object.callee.name === 'require') {
      if (id.type === 'Identifier') declared.add(id.name);
    }
  });

  // Helper to check if a declared name is used
  function isUsed(name) {
    return (used.get(name) || 0) > 0;
  }

  // Remove unused import specifiers / entire import if empty
  root.find(j.ImportDeclaration).forEach(path => {
    const node = path.node;
    const keepSpecs = node.specifiers.filter(spec => {
      const local = spec.local && spec.local.name;
      if (!local) return true;
      return isUsed(local);
    });

    if (keepSpecs.length === 0) {
      j(path).remove();
    } else if (keepSpecs.length !== node.specifiers.length) {
      node.specifiers = keepSpecs;
      j(path).replaceWith(node);
    }
  });

  // Remove unused require variable declarators
  root.find(j.VariableDeclaration).forEach(varPath => {
    const decls = varPath.node.declarations.slice();
    let changed = false;
    const remaining = [];

    decls.forEach(decl => {
      const id = decl.id;
      const init = decl.init;

      // only process require-based initializers (conservative)
      const isRequire =
        init && (
          (init.type === 'CallExpression' && init.callee && init.callee.name === 'require') ||
          (init.type === 'MemberExpression' && init.object && init.object.type === 'CallExpression' && init.object.callee && init.object.callee.name === 'require')
        );

      if (!isRequire) {
        remaining.push(decl);
        return;
      }

      if (id.type === 'Identifier') {
        if (isUsed(id.name)) remaining.push(decl);
        else changed = true;
        return;
      }

      if (id.type === 'ObjectPattern') {
        // filter properties
        const props = id.properties.filter(prop => {
          const localName = (prop.value && prop.value.name) || (prop.key && prop.key.name);
          return localName && isUsed(localName);
        });

        if (props.length > 0) {
          id.properties = props;
          remaining.push(decl);
        } else {
          changed = true;
        }
        return;
      }

      // fallback: keep
      remaining.push(decl);
    });

    if (changed) {
      if (remaining.length === 0) {
        j(varPath).remove();
      } else if (remaining.length !== varPath.node.declarations.length) {
        varPath.node.declarations = remaining;
        j(varPath).replaceWith(varPath.node);
      }
    }
  });

  return root.toSource({ quote: 'single' });
};