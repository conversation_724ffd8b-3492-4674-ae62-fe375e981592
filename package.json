{"name": "ryvyl-backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "nodemon index.js", "start-soap": "nodemon server.js", "lint": "eslint \"**/*.js\" --ignore-pattern \"node_modules/**\"", "lint:fix": "eslint \"**/*.js\" --fix --ignore-pattern \"node_modules/**\"", "remove-unused-imports": "npx jscodeshift -t scripts/remove-unused-imports.js --extensions=js --parser=babel . --ignore-pattern \"node_modules/**\""}, "author": "", "description": "", "dependencies": {"axios": "^1.7.9", "base-64": "^1.0.0", "base64url": "^3.0.1", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "body-parser": "^1.20.3", "clamscan": "^2.4.0", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.5.0", "ejs": "^3.1.10", "express": "^4.21.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^8.0.1", "express-session": "^1.18.1", "express-validator": "^7.2.1", "futoin-hkdf": "^1.5.3", "helmet": "^8.0.0", "joi": "^18.0.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.0", "morgan": "^1.10.0", "multer": "1.4.5-lts.1", "node-uuid": "^1.4.8", "nodemailer": "^6.9.15", "nodemon": "^3.1.7", "passport": "^0.7.0", "passport-local": "^1.0.0", "passport-local-mongoose": "^8.0.0", "postmark": "^4.0.5", "qrcode": "^1.5.4", "soap": "^1.1.10", "speakeasy": "^2.0.0", "twilio": "^5.5.2", "uuid": "^11.1.0", "xml": "^1.0.1", "zod": "^3.25.75"}, "devDependencies": {"eslint": "^8.57.1", "eslint-plugin-unused-imports": "^4.2.0", "jscodeshift": "^17.3.0"}}