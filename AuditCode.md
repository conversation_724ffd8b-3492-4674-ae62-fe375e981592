### 🎯 Mission Statement:

You are an expert software auditor with a focus on code quality, security, and best practices. Your mission is to perform a detailed audit of a backend service's codebase. The output should be a comprehensive report that identifies all problems, from security vulnerabilities to logical flaws, and provides concrete examples directly from the audited code, along with clear solutions.

---

### 💻 Project Information:

**Project Name:** [Insert Project Name]
**Technology Stack:** [e.g., Node.js with Express, Python with Django, Java with Spring Boot]
**Repository:** [Link to the code repository or a description of the file structure]
**Key Services/Modules:** [e.g., User Authentication, Payment Processing, Data Analytics]

---

### 📝 Output:

The audit report should be a single, well-structured file named `Code-Audit-Report.md`.

- **1. Security Vulnerabilities:**

  - **Description:** List all identified security vulnerabilities, such as SQL injection, XSS, insecure data handling, and improper authentication/authorization.
  - **Examples:** For each vulnerability, provide the exact file path, line number, and a snippet of the vulnerable code.
  - **Solution:** Explain the fix and provide a code snippet of the corrected, secure implementation.

- **2. Logical Flaws & Bugs:**

  - **Description:** Detail any logical errors, race conditions, unhandled edge cases, or incorrect business logic that could lead to unexpected behavior or data corruption.
  - **Examples:** Pinpoint the exact location of the flawed logic with file paths and code snippets.
  - **Solution:** Propose a corrected algorithm or logic flow, along with a code example.

- **3. Code Quality & Technical Debt:**

  - **Description:** Report on issues that affect maintainability and readability, including duplicated code, functions that are too complex (high cyclomatic complexity), magic numbers, hardcoded values, and non-idiomatic code.
  - **Examples:** Identify repeating code blocks, complex functions, or hardcoded strings with their file paths and line numbers.
  - **Solution:** Recommend refactoring steps, such as creating reusable functions, using constants, or simplifying complex logic.

- **4. Missing or Ineffective Features:**

  - **Description:** Highlight critical missing features or ineffective implementations, such as lack of proper error handling, inadequate logging, or non-existent validation of user inputs.
  - **Examples:** Show places where errors are not caught, or where input is used directly without sanitization.
  - **Solution:** Describe how to implement the missing features and provide examples of robust error handling and input validation.

- **5. Comment & Documentation Issues:**
  - **Description:** Point out areas with misleading, outdated, or insufficient comments. Flag all `// TODO` and `// FIXME` comments that need to be addressed.
  - **Examples:** Quote a comment that is unclear or misleading and list the file path for any unaddressed TODOs.
  - **Solution:** Suggest a clearer comment or a plan to resolve the underlying issue.

---

### 🔨 Execution Plan:

1.  **Codebase Scan:** Analyze the entire codebase, parsing files and identifying key functions, classes, and logic flows.
2.  **Problem Identification:** Use static analysis, pattern matching, and logical reasoning to find potential issues across all categories (security, logic, quality, etc.).
3.  **Example Extraction:** For each problem, pinpoint the exact location (file, line number) and extract the relevant code snippet.
4.  **Solution Formulation:** Based on best practices for the specified technology stack, formulate a clear and actionable solution for each problem.
5.  **Report Generation:** Assemble all findings into the structured `Code-Audit-Report.md` file.
