# 🌍 <PERSON><PERSON>vyl Backend Environment Variables
# Copy this file and update with your actual values

# Core Application
NODE_ENV=<development|production|staging>
PORT=<your-port>


# Database
DATABASE_URL=<your-mongodb-url>

# Authentication & Security
JWT_SECRET=your-super-secure-jwt-secret-min-32-chars
REFRESH_SECRET=your-different-refresh-secret-for-refresh-tokens

# Default Values
DEFAULT_CURRENCY_CODE=<EUR,USD...>
DEFAULT_ACCOUNT_NUMBER=<DEFAULT_IBAN>

# Email Services (Postmark)
POSTMARK_API_KEY=your-postmark-api-key
POSTMARK_FROM_EMAIL=<EMAIL>
POSTMARK_TEMPLATE_ALIAS_LOW_BIN=low-bin-alert

# IT Card API Integration
IT_CARD_API_URL=<your-it-card-api-url>
IT_CARD_CERT=<path-to-it-card-cert>
IT_CARD_KEY=<path-to-it-card-key>

# External Services
CARD_AUTH_URL=<card_issuing_auth_url>
CARD_WEBHOOK=<card_issuing_auth_url_for_card_creation_webhook>

# JWT Certificates
PRIVATE_KEY_PEM=<private-key>
PUBLIC_KEY_PEM=<public-key>
ISSUSER_URL=https://your-issuer-url.com

# Legacy Email (if needed)
EMAIL_USER=<email-user>
EMAIL_PASS=<email-password>

# Development Settings
INCLUDE_TOKEN_IN_RESPONSE=true
