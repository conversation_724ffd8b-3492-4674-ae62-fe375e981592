# 🌍 Environment Variables Configuration

This document outlines all environment variables required for the Ryvyl Backend application, deployment methods, and configuration guidelines.

---

## 📋 Required Environment Variables

### 🔐 **Core Application Settings**

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `NODE_ENV` | ✅ | `development` | Application environment (`development`, `production`, `staging`) |
| `PORT` | ❌ | `3000` | Server port (auto-set by hosting providers like Plesk/Vercel) |
| `DATABASE_URL` | ✅ | - | MongoDB connection string |

### 🔑 **Authentication & Security**

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `JWT_SECRET` | ✅ | - | Secret key for JWT token signing (min 32 chars) |
| `REFRESH_SECRET` | ✅ | `"refresh-secret"` ⚠️ | Secret for refresh tokens (MUST be different from JWT_SECRET) |

### 📧 **Email Services**

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `POSTMARK_API_KEY` | ✅ | - | Postmark API key for email delivery |
| `POSTMARK_FROM_EMAIL` | ❌ | `"<EMAIL>"` | Default sender email address |
| `POSTMARK_TEMPLATE_ALIAS_LOW_BIN` | ❌ | `"low-bin-alert"` | Template for low BIN alerts |
| `EMAIL_USER` | ❌ | - | Legacy email user (if using SMTP) |
| `EMAIL_PASS` | ❌ | - | Legacy email password (if using SMTP) |

### 📱 **SMS & Communication**

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `TWILIO_ACCOUNT_SID` | ✅ | - | Twilio account SID for SMS services |
| `TWILIO_AUTH_TOKEN` | ✅ | - | Twilio authentication token |
| `TWILIO_PHONE_NUMBER` | ✅ | - | Twilio phone number for sending SMS |

### 🏦 **IT Card API Integration**

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `IT_CARD_API_URL` | ✅ | - | Base URL for IT Card API |
| `IT_CARD_CERT` | ❌ | - | Client certificate file for IT Card API |
| `IT_CARD_KEY` | ❌ | - | Client key file for IT Card API |
| `RYVL_API_CERT` | ❌ | - | Alternative certificate file |
| `RYVL_API_KEY` | ❌ | - | Alternative key file |

### 🔗 **External Services & Webhooks**

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `CARD_AUTH_URL` | ✅ | - | Card authentication service URL |
| `CARD_WEBHOOK` | ❌ | - | Webhook URL for card events |
| `CARD_WEBHOOK_URL` | ❌ | - | Alternative webhook URL |
| `ITCARD_TRANSACTIONS_PATH` | ❌ | `"/api/itcard/transactions"` | Path for transaction API |
| `ITCARD_TRANSACTIONS` | ❌ | - | Alternative transaction path |

### 🔐 **JWT & Certificates**

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `PRIVATE_KEY_PEM` | ✅ | - | Path to private key file for ES512 JWT signing |
| `ISSUSER_URL` | ✅ | - | JWT issuer URL |

### 🛠️ **Development & Debugging**

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `INCLUDE_TOKEN_IN_RESPONSE` | ❌ | `false` | Include tokens in API responses (dev only) |

---

## 🚀 Deployment Methods

### 1. **Vercel Deployment** (Current Primary Method)

The project includes `vercel.json` configuration for Vercel deployment:

```json
{
  "version": 2,
  "builds": [
    {
      "src": "index.js",
      "use": "@vercel/node",
      "config": { "includeFiles": ["dist/**"] }
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "index.js"
    }
  ]
}
```

**Deployment Steps:**
1. Install Vercel CLI: `npm i -g vercel`
2. Login: `vercel login`
3. Deploy: `vercel --prod`
4. Set environment variables in Vercel dashboard

**Environment Variables Setup in Vercel:**
- Go to Project Settings → Environment Variables
- Add all required variables from the table above
- Ensure `NODE_ENV=production`

### 2. **Plesk Hosting** (Alternative Method)

**Prerequisites:**
- Plesk with Node.js extension
- Node.js 16+ support
- MongoDB access

**Deployment Steps:**
1. **Upload Code:**
   - ZIP project files (exclude `node_modules`, `.env`)
   - Upload via Plesk File Manager or Git

2. **Configure Node.js Application:**
   - Application root: `/httpdocs` (or project folder)
   - Startup file: `index.js`
   - Application mode: `production`
   - Node.js version: 16+ (latest LTS recommended)

3. **Set Environment Variables:**
   ```
   NODE_ENV=production
   DATABASE_URL=mongodb+srv://username:<EMAIL>/dbname
   JWT_SECRET=your-super-secure-jwt-secret-min-32-chars
   REFRESH_SECRET=your-different-refresh-secret
   POSTMARK_API_KEY=your-postmark-api-key
   TWILIO_ACCOUNT_SID=your-twilio-sid
   TWILIO_AUTH_TOKEN=your-twilio-token
   TWILIO_PHONE_NUMBER=your-twilio-phone
   IT_CARD_API_URL=https://your-itcard-api.com
   CARD_AUTH_URL=https://your-card-auth.com
   PRIVATE_KEY_PEM=path/to/private.pem
   ISSUSER_URL=https://your-issuer.com
   ```

4. **Install Dependencies:**
   ```bash
   npm install --production
   ```

5. **Start Application:**
   - Plesk will automatically start the app
   - Monitor logs in Plesk Node.js section

### 3. **PM2 Process Manager** (Self-Hosted)

**Installation:**
```bash
npm install -g pm2
```

**Deployment:**
```bash
# Start application
pm2 start index.js --name ryvyl-backend --env production

# Save PM2 configuration
pm2 save

# Setup auto-restart on system reboot
pm2 startup
```

**PM2 Ecosystem File** (`ecosystem.config.js`):
```javascript
module.exports = {
  apps: [{
    name: 'ryvyl-backend',
    script: 'index.js',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
};
```

---

## 🔧 Environment Setup Examples

### **Development (.env)**
```env
NODE_ENV=development
PORT=3001
DATABASE_URL=mongodb://localhost:27017/ryvyl-dev
JWT_SECRET=dev-jwt-secret-min-32-characters-long
REFRESH_SECRET=dev-refresh-secret-different-from-jwt
POSTMARK_API_KEY=your-dev-postmark-key
TWILIO_ACCOUNT_SID=your-dev-twilio-sid
TWILIO_AUTH_TOKEN=your-dev-twilio-token
TWILIO_PHONE_NUMBER=+**********
IT_CARD_API_URL=https://api-dev.itcard.com
CARD_AUTH_URL=https://card-auth-dev.ryvyl.eu
PRIVATE_KEY_PEM=config/private-dev.pem
ISSUSER_URL=https://dev-issuer.ryvyl.eu
INCLUDE_TOKEN_IN_RESPONSE=true
```

### **Production**
```env
NODE_ENV=production
DATABASE_URL=mongodb+srv://prod-user:<EMAIL>/ryvyl-prod
JWT_SECRET=super-secure-production-jwt-secret-min-32-chars
REFRESH_SECRET=different-super-secure-refresh-secret
POSTMARK_API_KEY=live-postmark-api-key
TWILIO_ACCOUNT_SID=live-twilio-account-sid
TWILIO_AUTH_TOKEN=live-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********
IT_CARD_API_URL=https://api.itcard.com
CARD_AUTH_URL=https://card-auth.ryvyl.eu
PRIVATE_KEY_PEM=config/private-prod.pem
ISSUSER_URL=https://issuer.ryvyl.eu
```

### **Staging**
```env
NODE_ENV=staging
DATABASE_URL=mongodb+srv://staging-user:<EMAIL>/ryvyl-staging
JWT_SECRET=staging-jwt-secret-min-32-characters
REFRESH_SECRET=staging-refresh-secret-different
POSTMARK_API_KEY=staging-postmark-key
TWILIO_ACCOUNT_SID=staging-twilio-sid
TWILIO_AUTH_TOKEN=staging-twilio-token
TWILIO_PHONE_NUMBER=+**********
IT_CARD_API_URL=https://api-staging.itcard.com
CARD_AUTH_URL=https://card-auth-staging.ryvyl.eu
PRIVATE_KEY_PEM=config/private-staging.pem
ISSUSER_URL=https://staging-issuer.ryvyl.eu
```

---

## ⚠️ Security Considerations

### **Critical Security Issues:**
1. **Never commit `.env` files** to version control
2. **Remove `example.env`** from production deployments (contains real credentials)
3. **Use strong, unique secrets** for JWT_SECRET and REFRESH_SECRET
4. **Rotate secrets regularly** in production
5. **Use environment-specific databases** (separate dev/staging/prod)

### **Certificate Management:**
- Store certificate files (`*.pem`) securely
- Use different certificates for each environment
- Ensure proper file permissions (600) on certificate files
- Never commit certificates to version control

### **Database Security:**
- Use MongoDB Atlas with IP whitelisting
- Enable authentication and SSL/TLS
- Use separate databases for each environment
- Regular backups and monitoring

---

## 🔍 Validation & Testing

**Environment Validation Script:**
```javascript
// Add to your startup code
const requiredEnvVars = [
  'DATABASE_URL',
  'JWT_SECRET',
  'REFRESH_SECRET',
  'POSTMARK_API_KEY',
  'TWILIO_ACCOUNT_SID',
  'TWILIO_AUTH_TOKEN',
  'IT_CARD_API_URL',
  'CARD_AUTH_URL',
  'PRIVATE_KEY_PEM',
  'ISSUSER_URL'
];

requiredEnvVars.forEach(envVar => {
  if (!process.env[envVar]) {
    console.error(`❌ Missing required environment variable: ${envVar}`);
    process.exit(1);
  }
});

console.log('✅ All required environment variables are set');
```

**Health Check Endpoint:**
```javascript
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    environment: process.env.NODE_ENV,
    timestamp: new Date().toISOString(),
    version: require('./package.json').version
  });
});
```
