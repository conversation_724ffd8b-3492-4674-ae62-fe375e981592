const express = require("express")
const router = express.Router()
const B2BAccount = require("../models/B2BAccount")
const B2BIndividualAccounts = require("../models/B2BIndividualAccounts")
const {sendPostRequest} = require("../config/ApiInstense")
const {registerUser} = require("../controllers/userController");
const Account = require("../models/Account");
const sendCardCreationWebhook = require("../config/webhook");
const Card = require("../models/B2BCard");
const IndividualOnboarding = require("../models/IndividualOnboarding");
// ⚡ Example validation helpers — place these in a separate module if you want.
const VALIDATION_RULES = {
    MAX_LENGTHS: {
        companyName: 255,
        clientCode: 50,
        phoneNumber: 20,
        email: 255,
        nip: 20,
        regon: 20,
        embossedName: 30,
        companyIndustry: 100,
        companyNumber: 50,
        contactName: 100,
        contactRole: 50,
        countryOfIncorporation: 3, // ISO code
        companyWebsite: 255,
        typeOfBusiness: 50,
        cardUsage: 50,
        businessSector: 50,
        businessPurpose: 255,
        adminName: 100,
        adminRole: 50,
        adminEmail: 255,
        adminPhone: 20,

        street: 100,
        buildingNumber: 10,
        apartmentNumber: 10,
        city: 50,
        zipCode: 20,
        country: 3,
    },
}

const isNotEmptyString = (value) =>
    typeof value === 'string' && value.trim().length > 0

const isValidLength = (value, max) =>
    typeof value === 'string' && value.length <= max

const isValidEmail = (value) =>
    typeof value === 'string' &&
    /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)

const isValidPhoneNumber = (value) =>
    typeof value === 'string' && /^[0-9+\-() ]{7,20}$/.test(value)

const isValidUrl = (value) =>
    typeof value === 'string' && /^https?:\/\/.+\..+/.test(value)

const isValidDate = (value) => {
    const d = new Date(value)
    return !isNaN(d.getTime())
}

const validateB2BRegisterData = (data) => {
    const errors = []

    const {
        company_name,
        clientCode,
        company_phone,
        authPhoneNumber,
        company_email,
        nip,
        regon,
        embossedName,

        company_industry,
        company_number,
        registration_date,
        contact_name,
        contact_role,
        country_of_incorporation,
        company_website,

        type_of_business,
        card_usage,
        business_sector,
        business_purpose,

        admin_name,
        admin_role,
        admin_email,
        admin_phone,

        registered_address,
        operational_address,
    } = data

    if (!isNotEmptyString(company_name)) {
        errors.push('Company name is required')
    } else if (!isValidLength(company_name, VALIDATION_RULES.MAX_LENGTHS.companyName)) {
        errors.push(`Company name must not exceed ${VALIDATION_RULES.MAX_LENGTHS.companyName} characters`)
    }

    if (!isNotEmptyString(clientCode)) {
        errors.push('Client code is required')
    } else if (!isValidLength(clientCode, VALIDATION_RULES.MAX_LENGTHS.clientCode)) {
        errors.push(`Client code must not exceed ${VALIDATION_RULES.MAX_LENGTHS.clientCode} characters`)
    }

    if (!isNotEmptyString(company_phone)) {
        errors.push('Company phone is required')
    } else if (!isValidPhoneNumber(company_phone)) {
        errors.push('Invalid company phone format')
    }

    if (authPhoneNumber && (!isNotEmptyString(authPhoneNumber) || !isValidPhoneNumber(authPhoneNumber))) {
        errors.push('Invalid auth phone number format')
    }

    if (!isNotEmptyString(company_email)) {
        errors.push('Company email is required')
    } else if (!isValidEmail(company_email)) {
        errors.push('Invalid company email format')
    } else if (!isValidLength(company_email, VALIDATION_RULES.MAX_LENGTHS.email)) {
        errors.push(`Company email must not exceed ${VALIDATION_RULES.MAX_LENGTHS.email} characters`)
    }

    if (nip && (!isNotEmptyString(nip) || !isValidLength(nip, VALIDATION_RULES.MAX_LENGTHS.nip))) {
        errors.push(`NIP must not exceed ${VALIDATION_RULES.MAX_LENGTHS.nip} characters`)
    }

    if (regon && (!isNotEmptyString(regon) || !isValidLength(regon, VALIDATION_RULES.MAX_LENGTHS.regon))) {
        errors.push(`REGON must not exceed ${VALIDATION_RULES.MAX_LENGTHS.regon} characters`)
    }

    if (embossedName && (!isNotEmptyString(embossedName) || !isValidLength(embossedName, VALIDATION_RULES.MAX_LENGTHS.embossedName))) {
        errors.push(`Embossed name must not exceed ${VALIDATION_RULES.MAX_LENGTHS.embossedName} characters`)
    }

    if (company_industry && (!isNotEmptyString(company_industry) || !isValidLength(company_industry, VALIDATION_RULES.MAX_LENGTHS.companyIndustry))) {
        errors.push(`Company industry must not exceed ${VALIDATION_RULES.MAX_LENGTHS.companyIndustry} characters`)
    }

    if (company_number && (!isNotEmptyString(company_number) || !isValidLength(company_number, VALIDATION_RULES.MAX_LENGTHS.companyNumber))) {
        errors.push(`Company number must not exceed ${VALIDATION_RULES.MAX_LENGTHS.companyNumber} characters`)
    }

    if (registration_date && !isValidDate(registration_date)) {
        errors.push('Registration date must be valid')
    }

    if (contact_name && (!isNotEmptyString(contact_name) || !isValidLength(contact_name, VALIDATION_RULES.MAX_LENGTHS.contactName))) {
        errors.push(`Contact name must not exceed ${VALIDATION_RULES.MAX_LENGTHS.contactName} characters`)
    }

    if (contact_role && (!isNotEmptyString(contact_role) || !isValidLength(contact_role, VALIDATION_RULES.MAX_LENGTHS.contactRole))) {
        errors.push(`Contact role must not exceed ${VALIDATION_RULES.MAX_LENGTHS.contactRole} characters`)
    }

    if (country_of_incorporation && (!isNotEmptyString(country_of_incorporation) || !isValidLength(country_of_incorporation, VALIDATION_RULES.MAX_LENGTHS.countryOfIncorporation))) {
        errors.push(`Country of incorporation must not exceed ${VALIDATION_RULES.MAX_LENGTHS.countryOfIncorporation} characters`)
    }

    if (company_website && (!isValidUrl(company_website) || !isValidLength(company_website, VALIDATION_RULES.MAX_LENGTHS.companyWebsite))) {
        errors.push('Invalid or too long company website')
    }

    if (type_of_business && (!isNotEmptyString(type_of_business) || !isValidLength(type_of_business, VALIDATION_RULES.MAX_LENGTHS.typeOfBusiness))) {
        errors.push(`Type of business must not exceed ${VALIDATION_RULES.MAX_LENGTHS.typeOfBusiness} characters`)
    }

    if (card_usage && (!isNotEmptyString(card_usage) || !isValidLength(card_usage, VALIDATION_RULES.MAX_LENGTHS.cardUsage))) {
        errors.push(`Card usage must not exceed ${VALIDATION_RULES.MAX_LENGTHS.cardUsage} characters`)
    }

    if (business_sector && (!isNotEmptyString(business_sector) || !isValidLength(business_sector, VALIDATION_RULES.MAX_LENGTHS.businessSector))) {
        errors.push(`Business sector must not exceed ${VALIDATION_RULES.MAX_LENGTHS.businessSector} characters`)
    }

    if (business_purpose && (!isNotEmptyString(business_purpose) || !isValidLength(business_purpose, VALIDATION_RULES.MAX_LENGTHS.businessPurpose))) {
        errors.push(`Business purpose must not exceed ${VALIDATION_RULES.MAX_LENGTHS.businessPurpose} characters`)
    }

    if (!isNotEmptyString(admin_name)) {
        errors.push('Admin name is required')
    } else if (!isValidLength(admin_name, VALIDATION_RULES.MAX_LENGTHS.adminName)) {
        errors.push(`Admin name must not exceed ${VALIDATION_RULES.MAX_LENGTHS.adminName} characters`)
    }

    if (admin_role && (!isNotEmptyString(admin_role) || !isValidLength(admin_role, VALIDATION_RULES.MAX_LENGTHS.adminRole))) {
        errors.push(`Admin role must not exceed ${VALIDATION_RULES.MAX_LENGTHS.adminRole} characters`)
    }

    if (!isNotEmptyString(admin_email)) {
        errors.push('Admin email is required')
    } else if (!isValidEmail(admin_email)) {
        errors.push('Invalid admin email')
    } else if (!isValidLength(admin_email, VALIDATION_RULES.MAX_LENGTHS.adminEmail)) {
        errors.push(`Admin email must not exceed ${VALIDATION_RULES.MAX_LENGTHS.adminEmail} characters`)
    }

    if (!isNotEmptyString(admin_phone)) {
        errors.push('Admin phone is required')
    } else if (!isValidPhoneNumber(admin_phone)) {
        errors.push('Invalid admin phone')
    }

    const checkAddress = (address, prefix) => {
        if (!address) {
            errors.push(`${prefix} address is required`)
            return
        }
        if (!isNotEmptyString(address.street)) errors.push(`${prefix} street is required`)
        else if (!isValidLength(address.street, VALIDATION_RULES.MAX_LENGTHS.street)) errors.push(`${prefix} street too long`)

        if (!isNotEmptyString(address.building_number)) errors.push(`${prefix} building number is required`)
        else if (!isValidLength(address.building_number, VALIDATION_RULES.MAX_LENGTHS.buildingNumber)) errors.push(`${prefix} building number too long`)

        if (address.apartment_number && !isValidLength(address.apartment_number, VALIDATION_RULES.MAX_LENGTHS.apartmentNumber))
            errors.push(`${prefix} apartment number too long`)

        if (!isNotEmptyString(address.city)) errors.push(`${prefix} city is required`)
        else if (!isValidLength(address.city, VALIDATION_RULES.MAX_LENGTHS.city)) errors.push(`${prefix} city too long`)

        if (!isNotEmptyString(address.postal_code)) errors.push(`${prefix} postal code is required`)
        else if (!isValidLength(address.postal_code, VALIDATION_RULES.MAX_LENGTHS.zipCode)) errors.push(`${prefix} postal code too long`)

        if (!isNotEmptyString(address.country)) errors.push(`${prefix} country is required`)
        else if (!isValidLength(address.country, VALIDATION_RULES.MAX_LENGTHS.country)) errors.push(`${prefix} country too long`)
    }

    checkAddress(registered_address, 'Registered')
    if (operational_address) checkAddress(operational_address, 'Operational')

    return errors
}

// Get all B2B accounts
router.get("/", async (req, res) => {
    try {
        const accounts = await B2BAccount.find().populate("parentCompany", "company_name").sort({createdAt: -1})

        res.status(200).json({
            success: true,
            count: accounts.length,
            data: accounts,
        })
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Server error",
            error: error.message,
        })
    }
})

// Get a single B2B account
router.get("/:id", async (req, res) => {
    try {
        const account = await B2BAccount.findById(req.params.id).populate("parentCompany", "company_name")
            .populate("products")

        if (!account) {
            return res.status(404).json({
                success: false,
                message: "B2B account not found",
            })
        }

        res.status(200).json({
            success: true,
            data: account,
        })
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Server error",
            error: error.message,
        })
    }
})




router.post("/register", async (req, res) => {
    const validationErrors = validateB2BRegisterData(req.body)
    if (validationErrors.length) {
        return res.status(400).json({
            success: false,
            message: "Validation failed",
            errors: validationErrors,
        })
    }

    try {
        const {
            company_name: companyName,
            clientCode,
            company_phone: phoneNumber,
            authPhoneNumber,
            company_email: email,
            nip,
            regon,
            embossedName,

            company_industry: companyIndustry,
            company_number: companyNumber,
            registration_date: registrationDate,
            contact_name: contactName,
            contact_role: contactRole,
            country_of_incorporation: countryOfIncorporation,
            company_website: companyWebsite,

            type_of_business: typeOfBusiness,
            card_usage: cardUsage,
            cardholder_groups: cardholderGroups,
            fund_loading: fundLoading,

            business_sector: businessSector,
            regions,
            countries,
            business_purpose: businessPurpose,
            card_user_groups: cardUserGroups,
            number_of_cards: numberOfCards,
            monthly_loading_value: monthlyLoadingValue,

            admin_name: adminName,
            admin_role: adminRole,
            admin_email: adminEmail,
            admin_phone: adminPhone,

            registered_address,
            operational_address,
        } = req.body

        const addressArray = []

        if (registered_address) {
            addressArray.push({
                type: "registration_address",
                street: registered_address.street,
                buildingNumber: registered_address.building_number,
                apartmentNumber: registered_address.apartment_number || "",
                city: registered_address.city,
                zipCode: registered_address.postal_code,
                country: registered_address.country,
            })
        }

        if (operational_address) {
            addressArray.push({
                type: "operational_address",
                street: operational_address.street,
                buildingNumber: operational_address.building_number,
                apartmentNumber: operational_address.apartment_number || "",
                city: operational_address.city,
                zipCode: operational_address.postal_code,
                country: operational_address.country,
            })
        }

        const newAccount = new B2BAccount({
            companyName,
            clientCode,
            phoneNumber,
            authPhoneNumber,
            email,
            addresses: addressArray,
            nip,
            regon,
            embossedName,
            parentCompany: "6785126247b8a6a67fbf7cad",

            companyIndustry,
            companyNumber,
            registrationDate: registrationDate ? new Date(registrationDate) : undefined,
            contactName,
            contactRole,
            countryOfIncorporation,
            companyWebsite,

            typeOfBusiness,
            cardUsage,
            cardholderGroups,
            fundLoading: fundLoading ? Number.parseFloat(fundLoading) : undefined,

            businessSector,
            regions,
            countries,
            businessPurpose,
            cardUserGroups,
            numberOfCards: numberOfCards ? Number.parseInt(numberOfCards) : undefined,
            monthlyLoadingValue: monthlyLoadingValue ? Number.parseFloat(monthlyLoadingValue) : undefined,

            adminName,
            adminRole,
            adminEmail,
            adminPhone,

            applicationSource: "web_form",
            status: "pending",
        })

        // 👉 FIRST: Try saving to DB
        const savedAccount = await newAccount.save()

        // 👉 SECOND: Prepare payload for external API
        const clientPayload = {
            companyName,
            clientCode,
            phoneNumber,
            authPhoneNumber,
            email,
            address: registered_address
                ? {
                    street: registered_address.street,
                    buildingNumber: registered_address.building_number,
                    apartmentNumber: registered_address.apartment_number,
                    city: registered_address.city,
                    zipCode: registered_address.postal_code,
                    country: registered_address.country,
                }
                : null,
            nip,
            regon,
            embossedName,
            company: true,
            customer: false,
        }

        const result = await sendPostRequest(
            "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/clients",
            clientPayload,
        )

        // Update DB record with external API response
        savedAccount.externalApiResponse = result
        if (result.clientId) {
            savedAccount.externalClientId = result.clientId
        }

        if (result.status?.toLowerCase() === "approved") {
            savedAccount.status = "approved"
            savedAccount.approvedAt = new Date()
        } else {
            savedAccount.status = "rejected"
            savedAccount.rejectedAt = new Date()
            savedAccount.rejectionReason = result.message || "External API rejection"
        }

        await savedAccount.save()

        if (savedAccount.status === "approved") {
            await createAccount(clientCode, savedAccount._id)
            await registerUser(companyName, email, [], "active", "corporate", savedAccount._id)

            return res.status(201).json({
                success: true,
                message: "B2B account created successfully",
                data: savedAccount,
                apiResponse: result,
            })
        } else {
            return res.status(400).json({
                success: false,
                message: "Failed to create B2B account in external system",
                error: result,
            })
        }
    } catch (error) {
        if (error.name === "ValidationError") {
            const messages = Object.values(error.errors).map((val) => val.message)
            return res.status(400).json({
                success: false,
                message: "Validation error",
                errors: messages,
            })
        }

        if (error.code === 11000) {
            const duplicateField = Object.keys(error.keyValue)[0]
            return res.status(400).json({
                success: false,
                message: "Duplicate entry found",
                error: `${duplicateField} already exists`,
            })
        }

        return res.status(500).json({
            success: false,
            message: "Server error",
            error: error.message,
        })
    }
})

async function createAccount(clientId, companyId) {

    const debitPayload = {
        currencyCode: "EUR",
        accNo: "**********************",
        owners: [
            {
                clientCode: clientId,
                relationship: "OWN",
            },
        ],
    }

    const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/accounts/debitAccount"


    try {
        const result = await sendPostRequest(url, debitPayload);

        if (!result.accNo) {
            return;
        }

        const accountData = {
            accountNumber: result.accNo,
            status: result.status,
            isB2b: true,
            b2bCompany: companyId,
            currencyCode: result.currencyCode,
            currencyName: result.currencyName,
            owners: result.owners.map((owner) => ({
                clientCode: clientId,
                relationship: owner.relationship,
                mainOwner: owner.mainOwner,
            })),
        };

        const newAccount = new Account(accountData);
        await newAccount.save();
    } catch (err) {}
}

// Additional endpoints for managing B2B accounts
router.get("/accounts", async (req, res) => {
    try {
        const {status, page = 1, limit = 10} = req.query
        const query = status ? {status} : {}

        const accounts = await B2BAccount.find(query)
            .populate("parentCompany", "name")
            .sort({createdAt: -1})
            .limit(limit * 1)
            .skip((page - 1) * limit)

        const total = await B2BAccount.countDocuments(query)

        res.json({
            success: true,
            data: accounts.map((account) => account.getSummary()),
            pagination: {
                page: Number.parseInt(page),
                limit: Number.parseInt(limit),
                total,
                pages: Math.ceil(total / limit),
            },
        })
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Error fetching accounts",
            error: error.message,
        })
    }
})


// Middleware to validate account ID and fetch account
const validatebusinessId = async (req, res, next) => {
    try {
        const {businessId} = req.params

        if (!mongoose.Types.ObjectId.isValid(businessId)) {
            return res.status(400).json({
                success: false,
                message: "Invalid account ID format",
            })
        }

        const account = await B2BAccount.findById(businessId)
        if (!account) {
            return res.status(404).json({
                success: false,
                message: "B2B account not found",
            })
        }

        req.account = account
        next()
    } catch (error) {
        return res.status(500).json({
            success: false,
            message: "Error validating account",
            error: error.message,
        })
    }
}


router.patch("/:businessId/company-info", validatebusinessId, async (req, res) => {
    try {
        const {
            company_name: companyName,
            company_industry: companyIndustry,
            company_number: companyNumber,
            registration_date: registrationDate,
            contact_name: contactName,
            contact_role: contactRole,
            country_of_incorporation: countryOfIncorporation,
            company_website: companyWebsite,
            company_phone: phoneNumber,
            company_email: email,
        } = req.body

        const updateData = {}

        // Only update provided fields
        if (companyName !== undefined) updateData.companyName = companyName
        if (companyIndustry !== undefined) updateData.companyIndustry = companyIndustry
        if (companyNumber !== undefined) updateData.companyNumber = companyNumber
        if (registrationDate !== undefined) {
            updateData.registrationDate = registrationDate ? new Date(registrationDate) : null
        }
        if (contactName !== undefined) updateData.contactName = contactName
        if (contactRole !== undefined) updateData.contactRole = contactRole
        if (countryOfIncorporation !== undefined) updateData.countryOfIncorporation = countryOfIncorporation
        if (companyWebsite !== undefined) updateData.companyWebsite = companyWebsite
        if (phoneNumber !== undefined) updateData.phoneNumber = phoneNumber
        if (email !== undefined) updateData.email = email

        const updatedAccount = await B2BAccount.findByIdAndUpdate(req.account._id, updateData, {
            new: true,
            runValidators: true,
        })

        res.json({
            success: true,
            message: "Company information updated successfully",
            data: {
                id: updatedAccount._id,
                companyName: updatedAccount.companyName,
                companyIndustry: updatedAccount.companyIndustry,
                companyNumber: updatedAccount.companyNumber,
                registrationDate: updatedAccount.registrationDate,
                contactName: updatedAccount.contactName,
                contactRole: updatedAccount.contactRole,
                countryOfIncorporation: updatedAccount.countryOfIncorporation,
                companyWebsite: updatedAccount.companyWebsite,
                phoneNumber: updatedAccount.phoneNumber,
                email: updatedAccount.email,
                updatedAt: updatedAccount.updatedAt,
            },
        })
    } catch (error) {
        if (error.name === "ValidationError") {
            const messages = Object.values(error.errors).map((val) => val.message)
            return res.status(400).json({
                success: false,
                message: "Validation error",
                errors: messages,
            })
        }

        if (error.code === 11000) {
            const duplicateField = Object.keys(error.keyValue)[0]
            return res.status(400).json({
                success: false,
                message: "Duplicate entry found",
                error: `${duplicateField} already exists`,
            })
        }

        res.status(500).json({
            success: false,
            message: "Error updating company information",
            error: error.message,
        })
    }
})


router.patch("/:businessId/addresses", validatebusinessId, async (req, res) => {
    try {
        const {registered_address, operational_address} = req.body

        const account = req.account
        const addressArray = [...account.addresses]

        // Update registered address
        if (registered_address) {
            const regAddressIndex = addressArray.findIndex((addr) => addr.type === "registration_address")
            const newRegAddress = {
                type: "registration_address",
                street: registered_address.street,
                buildingNumber: registered_address.building_number,
                apartmentNumber: registered_address.apartment_number || "",
                city: registered_address.city,
                zipCode: registered_address.postal_code,
                country: registered_address.country,
            }

            if (regAddressIndex >= 0) {
                addressArray[regAddressIndex] = newRegAddress
            } else {
                addressArray.push(newRegAddress)
            }
        }

        // Update operational address
        if (operational_address) {
            const opAddressIndex = addressArray.findIndex((addr) => addr.type === "delivery_address")
            const newOpAddress = {
                type: "operational_address",
                street: operational_address.street,
                buildingNumber: operational_address.building_number,
                apartmentNumber: operational_address.apartment_number || "",
                city: operational_address.city,
                zipCode: operational_address.postal_code,
                country: operational_address.country,
            }

            if (opAddressIndex >= 0) {
                addressArray[opAddressIndex] = newOpAddress
            } else {
                addressArray.push(newOpAddress)
            }
        }

        const updatedAccount = await B2BAccount.findByIdAndUpdate(
            account._id,
            {addresses: addressArray},
            {new: true, runValidators: true},
        )

        res.json({
            success: true,
            message: "Address information updated successfully",
            data: {
                id: updatedAccount._id,
                addresses: updatedAccount.addresses,
                registeredAddress: updatedAccount.getRegistrationAddress(),
                operationalAddress: updatedAccount.getDeliveryAddress(),
                updatedAt: updatedAccount.updatedAt,
            },
        })
    } catch (error) {
        if (error.name === "ValidationError") {
            const messages = Object.values(error.errors).map((val) => val.message)
            return res.status(400).json({
                success: false,
                message: "Validation error",
                errors: messages,
            })
        }

        res.status(500).json({
            success: false,
            message: "Error updating address information",
            error: error.message,
        })
    }
})


router.patch("/:businessId/identity", validatebusinessId, async (req, res) => {
    try {
        const {nip, regon, embossedName, clientCode, authPhoneNumber} = req.body

        const updateData = {}

        if (nip !== undefined) updateData.nip = nip
        if (regon !== undefined) updateData.regon = regon
        if (embossedName !== undefined) updateData.embossedName = embossedName
        if (clientCode !== undefined) updateData.clientCode = clientCode
        if (authPhoneNumber !== undefined) updateData.authPhoneNumber = authPhoneNumber

        const updatedAccount = await B2BAccount.findByIdAndUpdate(req.account._id, updateData, {
            new: true,
            runValidators: true,
        })

        res.json({
            success: true,
            message: "Identity information updated successfully",
            data: {
                id: updatedAccount._id,
                nip: updatedAccount.nip,
                regon: updatedAccount.regon,
                embossedName: updatedAccount.embossedName,
                clientCode: updatedAccount.clientCode,
                authPhoneNumber: updatedAccount.authPhoneNumber,
                updatedAt: updatedAccount.updatedAt,
            },
        })
    } catch (error) {
        if (error.name === "ValidationError") {
            const messages = Object.values(error.errors).map((val) => val.message)
            return res.status(400).json({
                success: false,
                message: "Validation error",
                errors: messages,
            })
        }

        if (error.code === 11000) {
            const duplicateField = Object.keys(error.keyValue)[0]
            return res.status(400).json({
                success: false,
                message: "Duplicate entry found",
                error: `${duplicateField} already exists`,
            })
        }

        res.status(500).json({
            success: false,
            message: "Error updating identity information",
            error: error.message,
        })
    }
})


router.patch("/:businessId/card-programme", validatebusinessId, async (req, res) => {
    try {
        const {
            type_of_business: typeOfBusiness,
            card_usage: cardUsage,
            cardholder_groups: cardholderGroups,
            fund_loading: fundLoading,
            card_user_groups: cardUserGroups,
            number_of_cards: numberOfCards,
            monthly_loading_value: monthlyLoadingValue,
        } = req.body

        const updateData = {}

        if (typeOfBusiness !== undefined) updateData.typeOfBusiness = typeOfBusiness
        if (cardUsage !== undefined) updateData.cardUsage = cardUsage
        if (cardholderGroups !== undefined) updateData.cardholderGroups = cardholderGroups
        if (fundLoading !== undefined) {
            updateData.fundLoading = fundLoading ? Number.parseFloat(fundLoading) : null
        }
        if (cardUserGroups !== undefined) updateData.cardUserGroups = cardUserGroups
        if (numberOfCards !== undefined) {
            updateData.numberOfCards = numberOfCards ? Number.parseInt(numberOfCards) : null
        }
        if (monthlyLoadingValue !== undefined) {
            updateData.monthlyLoadingValue = monthlyLoadingValue ? Number.parseFloat(monthlyLoadingValue) : null
        }

        const updatedAccount = await B2BAccount.findByIdAndUpdate(req.account._id, updateData, {
            new: true,
            runValidators: true,
        })

        res.json({
            success: true,
            message: "Card programme information updated successfully",
            data: {
                id: updatedAccount._id,
                typeOfBusiness: updatedAccount.typeOfBusiness,
                cardUsage: updatedAccount.cardUsage,
                cardholderGroups: updatedAccount.cardholderGroups,
                fundLoading: updatedAccount.fundLoading,
                cardUserGroups: updatedAccount.cardUserGroups,
                numberOfCards: updatedAccount.numberOfCards,
                monthlyLoadingValue: updatedAccount.monthlyLoadingValue,
                updatedAt: updatedAccount.updatedAt,
            },
        })
    } catch (error) {
        if (error.name === "ValidationError") {
            const messages = Object.values(error.errors).map((val) => val.message)
            return res.status(400).json({
                success: false,
                message: "Validation error",
                errors: messages,
            })
        }

        res.status(500).json({
            success: false,
            message: "Error updating card programme information",
            error: error.message,
        })
    }
})


router.patch("/:businessId/business-case", validatebusinessId, async (req, res) => {
    try {
        const {business_sector: businessSector, regions, countries, business_purpose: businessPurpose} = req.body

        const updateData = {}

        if (businessSector !== undefined) updateData.businessSector = businessSector
        if (regions !== undefined) updateData.regions = regions
        if (countries !== undefined) updateData.countries = countries
        if (businessPurpose !== undefined) updateData.businessPurpose = businessPurpose

        const updatedAccount = await B2BAccount.findByIdAndUpdate(req.account._id, updateData, {
            new: true,
            runValidators: true,
        })

        res.json({
            success: true,
            message: "Business case information updated successfully",
            data: {
                id: updatedAccount._id,
                businessSector: updatedAccount.businessSector,
                regions: updatedAccount.regions,
                countries: updatedAccount.countries,
                businessPurpose: updatedAccount.businessPurpose,
                updatedAt: updatedAccount.updatedAt,
            },
        })
    } catch (error) {
        if (error.name === "ValidationError") {
            const messages = Object.values(error.errors).map((val) => val.message)
            return res.status(400).json({
                success: false,
                message: "Validation error",
                errors: messages,
            })
        }

        res.status(500).json({
            success: false,
            message: "Error updating business case information",
            error: error.message,
        })
    }
})


router.patch("/:businessId/administrator", validatebusinessId, async (req, res) => {
    try {
        const {
            admin_name: adminName,
            admin_role: adminRole,
            admin_email: adminEmail,
            admin_phone: adminPhone
        } = req.body

        const updateData = {}

        if (adminName !== undefined) updateData.adminName = adminName
        if (adminRole !== undefined) updateData.adminRole = adminRole
        if (adminEmail !== undefined) updateData.adminEmail = adminEmail
        if (adminPhone !== undefined) updateData.adminPhone = adminPhone

        const updatedAccount = await B2BAccount.findByIdAndUpdate(req.account._id, updateData, {
            new: true,
            runValidators: true,
        })

        res.json({
            success: true,
            message: "Administrator information updated successfully",
            data: {
                id: updatedAccount._id,
                adminName: updatedAccount.adminName,
                adminRole: updatedAccount.adminRole,
                adminEmail: updatedAccount.adminEmail,
                adminPhone: updatedAccount.adminPhone,
                adminContact: updatedAccount.adminContact,
                updatedAt: updatedAccount.updatedAt,
            },
        })
    } catch (error) {
        if (error.name === "ValidationError") {
            const messages = Object.values(error.errors).map((val) => val.message)
            return res.status(400).json({
                success: false,
                message: "Validation error",
                errors: messages,
            })
        }

        res.status(500).json({
            success: false,
            message: "Error updating administrator information",
            error: error.message,
        })
    }
})


router.patch("/:businessId/status", validatebusinessId, async (req, res) => {
    try {
        const {
            status,
            approval_notes: approvalNotes,
            approved_by: approvedBy,
            rejected_by: rejectedBy,
            rejection_reason: rejectionReason,
        } = req.body

        const updateData = {}

        if (status !== undefined) {
            updateData.status = status

            // Handle status-specific fields
            if (status === "approved") {
                updateData.approvedAt = new Date()
                if (approvedBy) updateData.approvedBy = approvedBy
                if (approvalNotes) updateData.approvalNotes = approvalNotes
            } else if (status === "rejected") {
                updateData.rejectedAt = new Date()
                if (rejectedBy) updateData.rejectedBy = rejectedBy
                if (rejectionReason) updateData.rejectionReason = rejectionReason
            }
        }

        if (approvalNotes !== undefined) updateData.approvalNotes = approvalNotes
        if (rejectionReason !== undefined) updateData.rejectionReason = rejectionReason

        const updatedAccount = await B2BAccount.findByIdAndUpdate(req.account._id, updateData, {
            new: true,
            runValidators: true,
        }).populate("approvedBy rejectedBy", "name email")

        res.json({
            success: true,
            message: "Status information updated successfully",
            data: {
                id: updatedAccount._id,
                status: updatedAccount.status,
                approvalNotes: updatedAccount.approvalNotes,
                approvedBy: updatedAccount.approvedBy,
                approvedAt: updatedAccount.approvedAt,
                rejectedBy: updatedAccount.rejectedBy,
                rejectedAt: updatedAccount.rejectedAt,
                rejectionReason: updatedAccount.rejectionReason,
                updatedAt: updatedAccount.updatedAt,
            },
        })
    } catch (error) {
        if (error.name === "ValidationError") {
            const messages = Object.values(error.errors).map((val) => val.message)
            return res.status(400).json({
                success: false,
                message: "Validation error",
                errors: messages,
            })
        }

        res.status(500).json({
            success: false,
            message: "Error updating status information",
            error: error.message,
        })
    }
})
router.get("/statistics", async (req, res) => {
    try {
        const stats = await B2BAccount.getStatistics()
        res.json({
            success: true,
            data: stats[0] || {total: 0, statusBreakdown: []},
        })
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Error fetching statistics",
            error: error.message,
        })
    }
})
// ✅ Update Tax Info (NIP and REGON)
router.patch("/:id/tax-info", async (req, res) => {
    const {nip, regon} = req.body;

    try {
        const updated = await B2BAccount.findByIdAndUpdate(
            req.params.id,
            {nip, regon, updatedAt: Date.now()},
            {new: true}
        );

        if (!updated) {
            return res.status(404).json({success: false, message: "Account not found"});
        }

        return res.status(200).json({
            success: true,
            message: "Tax information updated",
            data: updated,
        });
    } catch (error) {
        return res.status(500).json({
            success: false,
            message: "Failed to update tax info",
            error: error.message,
        });
    }
});

// Update a B2B account
router.put("/:id", async (req, res) => {
    try {
        const updatedAccount = await B2BAccount.findByIdAndUpdate(
            req.params.id,
            {...req.body, updatedAt: Date.now()},
            {new: true, runValidators: true},
        )

        if (!updatedAccount) {
            return res.status(404).json({
                success: false,
                message: "B2B account not found",
            })
        }

        res.status(200).json({
            success: true,
            message: "B2B account updated successfully",
            data: updatedAccount,
        })
    } catch (error) {
        // Handle validation errors
        if (error.name === "ValidationError") {
            const messages = Object.values(error.errors).map((val) => val.message)
            return res.status(400).json({
                success: false,
                message: "Validation error",
                errors: messages,
            })
        }

        res.status(500).json({
            success: false,
            message: "Server error",
            error: error.message,
        })
    }
})

// Delete a B2B account
router.delete("/:id", async (req, res) => {
    try {
        const deletedAccount = await B2BAccount.findByIdAndDelete(req.params.id)

        if (!deletedAccount) {
            return res.status(404).json({
                success: false,
                message: "B2B account not found",
            })
        }

        res.status(200).json({
            success: true,
            message: "B2B account deleted successfully",
        })
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Server error",
            error: error.message,
        })
    }
})


// Optimized create physical card route
router.post("/createCard/physical", async (req, res) => {
    try {
        const {
            clientCode, b2bClientId, embossName1, embossName2, phoneNumber,
            nickname, productCode, deliveryMethod, userId
        } = req.body;

        const individual = await IndividualOnboarding.findById(userId).populate("b2bClient");
        const account = await B2BIndividualAccounts.findOne({parent: individual._id});

        if (!account) {
            return res.status(404).json({
                success: false,
                message: "Account not found for the provided b2bClientId"
            });
        }

        const cardData = {
            holder: account.owners[0].clientCode,
            embossName2: individual.b2bClient.clientCode.split("-")[1] || "",
            productCode,
            visual: productCode,
            delivery: {deliveryType: "LETTER"},
            pinDelivery: {deliveryType: "NONE"},
            extAppId: "testextAppId",
            account: {
                accNo: "**********************",
                currencyCode: account.currencyCode,
                productCode: "RYV1",
                owners: account.owners.map(owner => ({
                    clientCode: owner.clientCode,
                    relationship: owner.relationship || "OWN"
                }))
            },
            "3dsAuthMethods": ["MOBILE_APP"]
        };

        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/debitCard";
        const result = await sendPostRequest(url, cardData);

        if (!result.cardKey) {
            return res.status(400).json({
                success: false,
                message: "Card creation failed",
                error: result
            });
        }

        const DbCardData = {
            cardHash: result.cardHash,
            cardKey: result.cardKey,
            expDate: result.expDate,
            status: result.status,
            statusCode: result.statusCode,
            kind: result.kind,
            productCode: result.productCode,
            productDesc: result.productDesc,
            main: result.main,
            holder: result.holder,
            accNo: result.accNo,
            embossName1: result.embossName1,
            cardMask: result.cardMask,
            parent: b2bClientId,
            authPhoneNumber: phoneNumber,
            cardholder: individual._id,
            nickName: nickname,
            deliveryMethod
        };

        const newCard = new Card(DbCardData);
        await newCard.save();

        await sendCardCreationWebhook({...result, deliveryMethod});

        return res.status(201).json({
            success: true,
            message: "Physical card created successfully",
            data: newCard
        });
    } catch (error) {
        return res.status(500).json({
            success: false,
            message: "Internal Server Error",
            error: error.message
        });
    }
});
router.post("/createCard/virtual", async (req, res) => {
    try {
        const {
            clientCode, clientId, userId, b2bClientId,
            nickname, productCode
        } = req.body;


        // Step 2: Fetch individual and populate b2bClient
        const individual = await IndividualOnboarding.findOne({clientID: clientId}).populate("b2bClient");
        // Step 1: Fetch account by clientCode from owners
        const account = await B2BIndividualAccounts.findOne({
            parent: individual._id
        });
        if (!account) {
            return res.status(404).json({
                success: false,
                message: "Account not found for the provided clientId"
            });
        }

        // Step 3: Prepare cardData payload
        const cardData = {
            holder: account.owners[0].clientCode,
            embossName2: individual.b2bClient.clientCode.split("-")[1] || "",
            productCode: productCode,
            visual: productCode,
            delivery: {deliveryType: "LETTER"},
            pinDelivery: {deliveryType: "NONE"},
            extAppId: "testextAppId",
            account: {
                accNo: "**********************",
                currencyCode: account.currencyCode,
                productCode: "RYV1",
                owners: account.owners.map(owner => ({
                    clientCode: owner.clientCode,
                    relationship: owner.relationship || "OWN"
                }))
            },
            "3dsAuthMethods": ["MOBILE_APP"]
        };

        // Step 4: Send to external API
        const url = "https://apifintech.sandbox.itcardpaymentservice.pl:30080/v1/cards/debitCard";
        const result = await sendPostRequest(url, cardData);

        if (!result.cardKey) {
            return res.status(400).json({
                success: false,
                message: "Card creation failed",
                error: result
            });
        }

        // Step 5: Save response to DB
        const DbCardData = {
            cardHash: result.cardHash,
            cardKey: result.cardKey,
            expDate: result.expDate,
            status: result.status,
            statusCode: result.statusCode,
            kind: result.kind,
            productCode: result.productCode,
            productDesc: result.productDesc,
            main: result.main,
            holder: result.holder,
            accNo: result.accNo,
            embossName1: result.embossName1,
            cardMask: result.cardMask,
            parent: b2bClientId,
            cardholder: individual._id,
            nickName: nickname,
        };

        const newCard = new Card(DbCardData);
        await newCard.save();

        // Step 6: Optional webhook
        await sendCardCreationWebhook({...result});

        // Step 7: Return success
        return res.status(201).json({
            success: true,
            message: "Virtual card created successfully",
            data: newCard
        });
    } catch (error) {
        return res.status(500).json({
            success: false,
            message: "Internal Server Error",
            error: error.message
        });
    }
});


// Get all B2B Cards
// Get all B2B Cards by parent (b2bClientId)
router.get("/cards/parent/:parentId", async (req, res) => {
    try {

        const account = await B2BAccount.findById(req.params.parentId);

        const cards = await Card.find({parent: req.params.parentId});


        if (!cards || cards.length === 0) {
            return res.status(404).json({
                success: false,
                message: "No cards found for this parent ID",
            });
        }

        res.status(200).json({
            success: true,
            count: cards.length,
            data: cards,
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Failed to fetch cards by parent ID",
            error: error.message,
        });
    }
});


// Get single B2B Card by ID
router.get("/cards/:id", async (req, res) => {
    try {
        const card = await Card.findById(req.params.id);
        if (!card) {
            return res.status(404).json({
                success: false,
                message: "Card not found"
            });
        }

        res.status(200).json({
            success: true,
            data: card
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Failed to fetch card",
            error: error.message
        });
    }
});

router.get("/all/", async (req, res) => {

    return res.status(200).json()
});
router.get("/:id/cardholders/", async (req, res) => {
    try {
        const accounts = await IndividualOnboarding.find({b2bClient: req.params.id});
        if (!accounts) {
            return res.status(404).json({
                success: false,
                message: "Accounts not found"
            });
        }


        res.status(200).json({
            success: true,
            data: accounts
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Failed to fetch cardholders",
            error: error.message
        });
    }
})


router.get("/cardholder/:id/cards/", async (req, res) => {
    try {



        //         success: false,
        //         message: "Cardholder not found"



        // Use clientID, which matches the 'holder' field in cards
        const cards = await Card.find({cardholder: req.params.id});

        res.status(200).json({
            success: true,
            data: cards
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Failed to fetch cards",
            error: error.message
        });
    }
});


const mongoose = require("mongoose");

router.post("/:id/products/assign", async (req, res) => {
    try {
        const {productIds} = req.body;

        if (!Array.isArray(productIds) || productIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: "No product IDs provided.",
            });
        }

        // Validate ObjectIds
        const validIds = productIds.filter(id => mongoose.Types.ObjectId.isValid(id));

        if (validIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: "No valid product IDs provided.",
            });
        }

        const updatedAccount = await B2BAccount.findByIdAndUpdate(
            req.params.id,
            {$addToSet: {products: {$each: validIds}}, updatedAt: Date.now()},
            {new: true}
        ).populate("products");


        if (!updatedAccount) {
            return res.status(404).json({
                success: false,
                message: "B2B account not found",
            });
        }

        res.status(200).json({
            success: true,
            message: "Products assigned successfully",
            data: updatedAccount,
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Internal server error",
            error: error.message,
        });
    }
});


router.get("/:id/products", async (req, res) => {
    try {
        const account = await B2BAccount.findById(req.params.id).populate("products");
        if (!account) {
            return res.status(404).json({
                success: false,
                message: "B2B account not found",
            });
        }

        res.status(200).json({
            success: true,
            account: account,
            products: account.products,
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Failed to fetch assigned products",
            error: error.message,
        });
    }
});

router.delete("/:id/products/:productId", async (req, res) => {
    try {
        const {id, productId} = req.params;

        // Validate ObjectIds
        if (!mongoose.Types.ObjectId.isValid(id) || !mongoose.Types.ObjectId.isValid(productId)) {
            return res.status(400).json({
                success: false,
                message: "Invalid B2B account ID or Product ID.",
            });
        }

        const updatedAccount = await B2BAccount.findByIdAndUpdate(
            id,
            {$pull: {products: productId}, updatedAt: Date.now()},
            {new: true}
        ).populate("products");

        if (!updatedAccount) {
            return res.status(404).json({
                success: false,
                message: "B2B account not found",
            });
        }

        res.status(200).json({
            success: true,
            message: "Product unassigned successfully",
            data: updatedAccount,
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: "Internal server error",
            error: error.message,
        });
    }
});

router.post("/updateAddress", async (req, res) => {
    const {
        clientId,
        building,
        street,
        apartment,
        city,
        stateProvince, // Not stored unless added in schema
        postalCode,
        country,
        type = "delivery_address", // Optional: fallback to delivery
    } = req.body;

    try {
        const client = await B2BAccount.findById(clientId);
        if (!client) {
            return res.status(404).json({success: false, message: "Client not found"});
        }

        // Prepare new address object
        const newAddress = {
            type: "delivery_address",
            street,
            buildingNumber: building,
            apartmentNumber: apartment || "",
            city,
            country,
            zipCode: postalCode,
        };

        // Find index of existing address with same type
        const index = client.addresses.findIndex(addr => addr.type === type);

        if (index > -1) {
            // Update existing address
            client.addresses[index] = newAddress;
        } else {
            // Add new address
            client.addresses.push(newAddress);
        }

        await client.save();

        return res.status(200).json({
            success: true,
            message: "Address updated successfully",
            data: client.addresses,
        });
    } catch (error) {
        return res.status(500).json({
            success: false,
            message: "Internal server error",
            error: error.message,
        });
    }
});


module.exports = router
