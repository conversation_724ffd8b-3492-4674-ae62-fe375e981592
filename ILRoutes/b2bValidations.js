const { VALIDATION_RULES } = require("../validations/b2bValidationSchema");
const {
    isNotEmpty,
    isValid<PERSON>ength,
    isValidEmail,
    isValidPhoneNumber,
    isValidUrl,
    isValidDate,
    checkAddress
} = require("../utils/validation-utils");

const validateB2BRegisterData = (data) => {
    const errors = []

    const {
        company_name,
        clientCode,
        company_phone,
        authPhoneNumber,
        company_email,
        nip,
        regon,
        embossedName,

        company_industry,
        company_number,
        registration_date,
        contact_name,
        contact_role,
        country_of_incorporation,
        company_website,

        type_of_business,
        card_usage,
        business_sector,
        business_purpose,

        admin_name,
        admin_role,
        admin_email,
        admin_phone,

        registered_address,
        operational_address,
    } = data

    // ✔ Company name
    if (!isNotEmpty(company_name)) {
        errors.push('Company name is required')
    } else if (!isValidLength(company_name, VALIDATION_RULES.MAX_LENGTHS.companyName)) {
        errors.push(`Company name must not exceed ${VALIDATION_RULES.MAX_LENGTHS.companyName} characters`)
    }

    // ✔ Client code
    if (!isNotEmpty(clientCode)) {
        errors.push('Client code is required')
    } else if (!isValidLength(clientCode, VALIDATION_RULES.MAX_LENGTHS.clientCode)) {
        errors.push(`Client code must not exceed ${VALIDATION_RULES.MAX_LENGTHS.clientCode} characters`)
    }

    // ✔ Phone number
    if (!isNotEmpty(company_phone)) {
        errors.push('Company phone is required')
    } else if (!isValidPhoneNumber(company_phone)) {
        errors.push('Invalid company phone format')
    }

    if (authPhoneNumber && (!isNotEmpty(authPhoneNumber) || !isValidPhoneNumber(authPhoneNumber))) {
        errors.push('Invalid auth phone number format')
    }

    // ✔ Email
    if (!isNotEmpty(company_email)) {
        errors.push('Company email is required')
    } else if (!isValidEmail(company_email)) {
        errors.push('Invalid company email format')
    } else if (!isValidLength(company_email, VALIDATION_RULES.MAX_LENGTHS.email)) {
        errors.push(`Company email must not exceed ${VALIDATION_RULES.MAX_LENGTHS.email} characters`)
    }

    // ✔ NIP & REGON
    if (nip !== undefined && nip !== null) {
        if (typeof nip !== 'string') errors.push('NIP must be a string')
        else if (!isValidLength(nip, VALIDATION_RULES.MAX_LENGTHS.nip)) {
            errors.push(`NIP must not exceed ${VALIDATION_RULES.MAX_LENGTHS.nip} characters`)
        }
    }

    if (regon !== undefined && regon !== null) {
        if (typeof regon !== 'string') errors.push('REGON must be a string')
        else if (!isValidLength(regon, VALIDATION_RULES.MAX_LENGTHS.regon)) {
            errors.push(`REGON must not exceed ${VALIDATION_RULES.MAX_LENGTHS.regon} characters`)
        }
    }

    if (embossedName && (!isNotEmpty(embossedName) || !isValidLength(embossedName, VALIDATION_RULES.MAX_LENGTHS.embossedName))) {
        errors.push(`Embossed name must not exceed ${VALIDATION_RULES.MAX_LENGTHS.embossedName} characters`)
    }

    if (company_industry && (!isNotEmpty(company_industry) || !isValidLength(company_industry, VALIDATION_RULES.MAX_LENGTHS.companyIndustry))) {
        errors.push(`Company industry must not exceed ${VALIDATION_RULES.MAX_LENGTHS.companyIndustry} characters`)
    }

    if (company_number && (!isNotEmpty(company_number) || !isValidLength(company_number, VALIDATION_RULES.MAX_LENGTHS.companyNumber))) {
        errors.push(`Company number must not exceed ${VALIDATION_RULES.MAX_LENGTHS.companyNumber} characters`)
    }

    if (registration_date && !isValidDate(registration_date)) {
        errors.push('Registration date must be a valid date')
    }

    if (contact_name && (!isNotEmpty(contact_name) || !isValidLength(contact_name, VALIDATION_RULES.MAX_LENGTHS.contactName))) {
        errors.push(`Contact name must not exceed ${VALIDATION_RULES.MAX_LENGTHS.contactName} characters`)
    }

    if (contact_role && (!isNotEmpty(contact_role) || !isValidLength(contact_role, VALIDATION_RULES.MAX_LENGTHS.contactRole))) {
        errors.push(`Contact role must not exceed ${VALIDATION_RULES.MAX_LENGTHS.contactRole} characters`)
    }

    if (country_of_incorporation && (!isNotEmpty(country_of_incorporation) || !isValidLength(country_of_incorporation, VALIDATION_RULES.MAX_LENGTHS.countryOfIncorporation))) {
        errors.push(`Country of incorporation must not exceed ${VALIDATION_RULES.MAX_LENGTHS.countryOfIncorporation} characters`)
    }

    if (company_website && (!isValidUrl(company_website) || !isValidLength(company_website, VALIDATION_RULES.MAX_LENGTHS.companyWebsite))) {
        errors.push(`Invalid or too long company website`)
    }

    if (type_of_business && (!isNotEmpty(type_of_business) || !isValidLength(type_of_business, VALIDATION_RULES.MAX_LENGTHS.typeOfBusiness))) {
        errors.push(`Type of business must not exceed ${VALIDATION_RULES.MAX_LENGTHS.typeOfBusiness} characters`)
    }

    if (card_usage && (!isNotEmpty(card_usage) || !isValidLength(card_usage, VALIDATION_RULES.MAX_LENGTHS.cardUsage))) {
        errors.push(`Card usage must not exceed ${VALIDATION_RULES.MAX_LENGTHS.cardUsage} characters`)
    }

    if (business_sector && (!isNotEmpty(business_sector) || !isValidLength(business_sector, VALIDATION_RULES.MAX_LENGTHS.businessSector))) {
        errors.push(`Business sector must not exceed ${VALIDATION_RULES.MAX_LENGTHS.businessSector} characters`)
    }

    if (business_purpose && (!isNotEmpty(business_purpose) || !isValidLength(business_purpose, VALIDATION_RULES.MAX_LENGTHS.businessPurpose))) {
        errors.push(`Business purpose must not exceed ${VALIDATION_RULES.MAX_LENGTHS.businessPurpose} characters`)
    }

    if (!isNotEmpty(admin_name)) {
        errors.push('Admin name is required')
    } else if (!isValidLength(admin_name, VALIDATION_RULES.MAX_LENGTHS.adminName)) {
        errors.push(`Admin name must not exceed ${VALIDATION_RULES.MAX_LENGTHS.adminName} characters`)
    }

    if (admin_role && (!isNotEmpty(admin_role) || !isValidLength(admin_role, VALIDATION_RULES.MAX_LENGTHS.adminRole))) {
        errors.push(`Admin role must not exceed ${VALIDATION_RULES.MAX_LENGTHS.adminRole} characters`)
    }

    if (!isNotEmpty(admin_email)) {
        errors.push('Admin email is required')
    } else if (!isValidEmail(admin_email)) {
        errors.push('Invalid admin email')
    } else if (!isValidLength(admin_email, VALIDATION_RULES.MAX_LENGTHS.adminEmail)) {
        errors.push(`Admin email must not exceed ${VALIDATION_RULES.MAX_LENGTHS.adminEmail} characters`)
    }

    if (!isNotEmpty(admin_phone)) {
        errors.push('Admin phone is required')
    } else if (!isValidPhoneNumber(admin_phone)) {
        errors.push('Invalid admin phone')
    }

    // ✔ Addresses (objects)
    errors.push(...checkAddress(data.registered_address, VALIDATION_RULES.MAX_LENGTHS, 'Registered '))
    if (data.operational_address) errors.push(...checkAddress(data.operational_address, VALIDATION_RULES.MAX_LENGTHS, 'Operational '))

    return errors
}

module.exports = { validateB2BRegisterData }
