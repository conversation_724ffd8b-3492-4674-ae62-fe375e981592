module.exports = [
  {
    files: ["**/*.js"],
    // language / parser options
    languageOptions: {
      ecmaVersion: 2021,
      sourceType: "module",
    },

    // plugins (require installed plugin)
    plugins: {
      "unused-imports": require("eslint-plugin-unused-imports"),
    },

    // rules
    rules: {
      // remove unused import statements
      "unused-imports/no-unused-imports": "error",
      // keep no-unused-vars but be lenient for underscore-prefixed variables
      "no-unused-vars": ["warn", { "argsIgnorePattern": "^_", "varsIgnorePattern": "^_" }],
    },
  },
];