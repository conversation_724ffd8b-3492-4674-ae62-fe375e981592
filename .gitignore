# Node modules
node_modules/

# Environment variables
.env
.env.local
.env.*.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage
coverage/
*.lcov

# Build
dist/
build/
.tmp/

# Cache
.cache/
.nyc_output/
.eslintcache
.stylelintcache
.turbo/
.vscode/

# OS files
.DS_Store
Thumbs.db

# IDE
.idea/
*.iml

# Optional npm cache
.npm


#keys
config/RYVL_API.crt
config/RYVL_API.key
# SSL certificates
private.pem
public.pem
